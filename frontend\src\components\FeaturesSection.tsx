import React from 'react'

const FeaturesSection: React.FC = () => {
  const features = [
    {
      id: 1,
      title: 'أفضل الأسعار',
      description: 'نضمن لك أفضل الأسعار في السوق مع إمكانية المقارنة الفورية',
      icon: '💰',
      color: 'from-green-400 to-green-600'
    },
    {
      id: 2,
      title: 'حجز آمن',
      description: 'نظام حجز آمن ومشفر بأحدث تقنيات الأمان العالمية',
      icon: '🔒',
      color: 'from-blue-400 to-blue-600'
    },
    {
      id: 3,
      title: 'دعم 24/7',
      description: 'فريق دعم العملاء متاح على مدار الساعة لمساعدتك',
      icon: '🎧',
      color: 'from-purple-400 to-purple-600'
    },
    {
      id: 4,
      title: 'إلغاء مجاني',
      description: 'إمكانية الإلغاء المجاني حتى 24 ساعة قبل موعد السفر',
      icon: '🔄',
      color: 'from-orange-400 to-orange-600'
    },
    {
      id: 5,
      title: 'تطبيق محمول',
      description: 'تطبيق سهل الاستخدام لإدارة حجوزاتك أثناء التنقل',
      icon: '📱',
      color: 'from-pink-400 to-pink-600'
    },
    {
      id: 6,
      title: 'مكافآت الولاء',
      description: 'اجمع النقاط مع كل حجز واحصل على مكافآت وخصومات حصرية',
      icon: '🎁',
      color: 'from-yellow-400 to-yellow-600'
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">لماذا تختار SkyWay؟</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            نحن نقدم تجربة سفر استثنائية مع مميزات لا تجدها في أي مكان آخر
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature) => (
            <div key={feature.id} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="text-center mb-6">
                <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r ${feature.color} flex items-center justify-center text-2xl`}>
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl p-8 shadow-xl max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-gray-800 mb-4">جاهز لبدء رحلتك؟</h3>
            <p className="text-xl text-gray-600 mb-8">
              انضم إلى ملايين المسافرين الذين يثقون في SkyWay لرحلاتهم حول العالم
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-gradient-to-r from-skyway-600 to-skyway-700 text-white px-8 py-4 rounded-lg font-bold text-lg hover:from-skyway-700 hover:to-skyway-800 transition-all duration-300 shadow-lg">
                ابدأ البحث الآن
              </button>
              <button className="border-2 border-skyway-600 text-skyway-600 px-8 py-4 rounded-lg font-bold text-lg hover:bg-skyway-600 hover:text-white transition-all duration-300">
                تحميل التطبيق
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FeaturesSection
