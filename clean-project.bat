@echo off
echo ========================================
echo    FlyWay - طرق السماء
echo    Complete Project Cleanup
echo ========================================
echo.

echo Checking for frontend folder...
if exist "frontend" (
    echo ❌ Frontend folder still exists! Removing...
    rd /s /q "frontend" 2>nul
    if exist "frontend" (
        echo ⚠️  Could not remove frontend folder. Please close all applications and try again.
    ) else (
        echo ✅ Frontend folder removed successfully!
    )
) else (
    echo ✅ Frontend folder already removed!
)

echo.
echo Checking current project structure...
dir /b

echo.
echo Current project contains:
echo ✅ backend/ - Node.js backend server
echo ✅ README.md - Project documentation
echo ❌ frontend/ - REMOVED (as requested)

echo.
echo Clearing any temporary files...
if exist "*.tmp" del /q "*.tmp" 2>nul
if exist "*.log" del /q "*.log" 2>nul

echo.
echo ========================================
echo 🎉 Project cleanup completed!
echo.
echo The frontend has been completely removed.
echo All 33 errors should disappear after restarting VS Code.
echo.
echo To restart VS Code and clear cache, run:
echo restart-vscode.bat
echo ========================================

pause
