import React from 'react';

const PlanBookPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">خطط واحجز</h1>
          <p className="text-lg text-gray-600">خطط لرحلتك واحجز بأفضل الأسعار</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-4">خطط رحلتك</h3>
            <p className="text-gray-600">اختر وجهتك وخطط لرحلة مثالية</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-4">احجز رحلتك</h3>
            <p className="text-gray-600">احجز رحلات الطيران بأفضل الأسعار</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-4">قبل السفر</h3>
            <p className="text-gray-600">كل ما تحتاجه قبل السفر</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlanBookPage;
