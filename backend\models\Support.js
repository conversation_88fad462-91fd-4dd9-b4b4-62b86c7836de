const mongoose = require('mongoose');

// FAQ Schema
const faqSchema = new mongoose.Schema({
  category: {
    type: String,
    enum: ['booking', 'payment', 'baggage', 'check-in', 'loyalty', 'refunds', 'general'],
    required: true
  },
  question: {
    ar: { type: String, required: true },
    en: { type: String, required: true }
  },
  answer: {
    ar: { type: String, required: true },
    en: { type: String, required: true }
  },
  tags: [String],
  order: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  viewCount: {
    type: Number,
    default: 0
  },
  helpful: {
    yes: { type: Number, default: 0 },
    no: { type: Number, default: 0 }
  }
}, {
  timestamps: true
});

// Contact Schema
const contactSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['office', 'call-center', 'airport', 'sales'],
    required: true
  },
  name: {
    ar: String,
    en: String
  },
  location: {
    country: { type: String, required: true },
    city: { type: String, required: true },
    address: {
      ar: String,
      en: String
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  contact: {
    phone: [String],
    fax: String,
    email: String,
    website: String
  },
  operatingHours: {
    weekdays: {
      open: String,
      close: String
    },
    weekends: {
      open: String,
      close: String
    },
    holidays: {
      open: String,
      close: String
    },
    timezone: String
  },
  services: [{
    type: String,
    enum: ['ticketing', 'customer-service', 'baggage-claim', 'lost-found', 'complaints', 'group-booking']
  }],
  languages: [String],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Support Ticket Schema
const supportTicketSchema = new mongoose.Schema({
  ticketNumber: {
    type: String,
    required: true,
    unique: true,
    uppercase: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  guestInfo: {
    name: String,
    email: String,
    phone: String
  },
  category: {
    type: String,
    enum: ['booking-issue', 'payment-problem', 'baggage-claim', 'refund-request', 'complaint', 'suggestion', 'technical-issue'],
    required: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  subject: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  bookingReference: String,
  attachments: [{
    filename: String,
    url: String,
    size: Number,
    type: String
  }],
  status: {
    type: String,
    enum: ['open', 'in-progress', 'waiting-customer', 'resolved', 'closed'],
    default: 'open'
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User' // Support agent
  },
  responses: [{
    from: {
      type: String,
      enum: ['customer', 'agent'],
      required: true
    },
    message: {
      type: String,
      required: true
    },
    attachments: [{
      filename: String,
      url: String
    }],
    timestamp: {
      type: Date,
      default: Date.now
    },
    isInternal: {
      type: Boolean,
      default: false
    }
  }],
  resolution: {
    summary: String,
    resolvedAt: Date,
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    customerSatisfaction: {
      rating: {
        type: Number,
        min: 1,
        max: 5
      },
      feedback: String
    }
  },
  tags: [String],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Generate ticket number before saving
supportTicketSchema.pre('save', function(next) {
  if (!this.ticketNumber) {
    this.ticketNumber = 'FW' + Date.now().toString().slice(-6) + Math.random().toString(36).substr(2, 2).toUpperCase();
  }
  next();
});

// Special Services Schema
const specialServiceSchema = new mongoose.Schema({
  category: {
    type: String,
    enum: ['elderly', 'children', 'pregnant', 'disabled', 'medical', 'dietary'],
    required: true
  },
  name: {
    ar: { type: String, required: true },
    en: { type: String, required: true }
  },
  description: {
    ar: { type: String, required: true },
    en: { type: String, required: true }
  },
  requirements: [{
    ar: String,
    en: String
  }],
  process: [{
    step: Number,
    description: {
      ar: String,
      en: String
    }
  }],
  documentation: [{
    name: {
      ar: String,
      en: String
    },
    required: Boolean,
    description: {
      ar: String,
      en: String
    }
  }],
  fee: {
    amount: { type: Number, default: 0 },
    currency: { type: String, default: 'USD' },
    description: {
      ar: String,
      en: String
    }
  },
  advanceNotice: {
    hours: { type: Number, default: 24 },
    description: {
      ar: String,
      en: String
    }
  },
  contact: {
    phone: String,
    email: String,
    department: String
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Index for efficient searching
faqSchema.index({ category: 1, isActive: 1 });
faqSchema.index({ tags: 1 });
contactSchema.index({ type: 1, 'location.country': 1 });
supportTicketSchema.index({ ticketNumber: 1 });
supportTicketSchema.index({ user: 1, status: 1 });
specialServiceSchema.index({ category: 1, isActive: 1 });

const FAQ = mongoose.model('FAQ', faqSchema);
const Contact = mongoose.model('Contact', contactSchema);
const SupportTicket = mongoose.model('SupportTicket', supportTicketSchema);
const SpecialService = mongoose.model('SpecialService', specialServiceSchema);

module.exports = {
  FAQ,
  Contact,
  SupportTicket,
  SpecialService
};
