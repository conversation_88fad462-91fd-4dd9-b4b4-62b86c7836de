const Destination = require('../models/Destination');
const { asyncHandler } = require('../middlewares/errorHandler');

// @desc    Get all destinations
// @route   GET /api/destinations
// @access  Public
const getAllDestinations = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;

  // Build filter
  const filter = { isActive: true };
  
  if (req.query.continent) {
    filter.continent = req.query.continent;
  }
  
  if (req.query.country) {
    filter.country = new RegExp(req.query.country, 'i');
  }
  
  if (req.query.city) {
    filter.city = new RegExp(req.query.city, 'i');
  }

  if (req.query.popular === 'true') {
    filter.isPopular = true;
  }

  // Sort options
  let sortOption = {};
  if (req.query.sort === 'popular') {
    sortOption = { isPopular: -1, searchCount: -1 };
  } else if (req.query.sort === 'name') {
    sortOption = { city: 1 };
  } else {
    sortOption = { searchCount: -1 };
  }

  const destinations = await Destination.find(filter)
    .sort(sortOption)
    .skip(skip)
    .limit(limit)
    .select('-__v');

  const total = await Destination.countDocuments(filter);

  res.status(200).json({
    success: true,
    data: {
      destinations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Get destination by ID or airport code
// @route   GET /api/destinations/:identifier
// @access  Public
const getDestination = asyncHandler(async (req, res) => {
  const { identifier } = req.params;
  
  let destination;
  
  // Check if identifier is airport code (3 letters) or ObjectId
  if (identifier.length === 3) {
    destination = await Destination.findOne({ 
      'airport.code': identifier.toUpperCase(),
      isActive: true 
    });
  } else {
    destination = await Destination.findById(identifier);
  }

  if (!destination) {
    return res.status(404).json({
      success: false,
      message: 'الوجهة غير موجودة'
    });
  }

  // Increment search count
  destination.searchCount += 1;
  await destination.save();

  res.status(200).json({
    success: true,
    data: {
      destination
    }
  });
});

// @desc    Search destinations
// @route   GET /api/destinations/search
// @access  Public
const searchDestinations = asyncHandler(async (req, res) => {
  const { q, limit = 10 } = req.query;

  if (!q || q.length < 2) {
    return res.status(400).json({
      success: false,
      message: 'يجب أن يكون البحث على الأقل حرفين'
    });
  }

  const searchRegex = new RegExp(q, 'i');
  
  const destinations = await Destination.find({
    $and: [
      { isActive: true },
      {
        $or: [
          { city: searchRegex },
          { country: searchRegex },
          { 'airport.code': searchRegex },
          { 'airport.name': searchRegex }
        ]
      }
    ]
  })
  .limit(parseInt(limit))
  .select('city country airport.code airport.name isPopular')
  .sort({ isPopular: -1, searchCount: -1 });

  res.status(200).json({
    success: true,
    data: {
      destinations,
      count: destinations.length
    }
  });
});

// @desc    Get popular destinations
// @route   GET /api/destinations/popular
// @access  Public
const getPopularDestinations = asyncHandler(async (req, res) => {
  const limit = parseInt(req.query.limit) || 6;

  const destinations = await Destination.find({
    isPopular: true,
    isActive: true
  })
  .sort({ searchCount: -1, bookingCount: -1 })
  .limit(limit)
  .select('city country airport.code images isPopular searchCount bookingCount');

  res.status(200).json({
    success: true,
    data: {
      destinations
    }
  });
});

// @desc    Get destinations by continent
// @route   GET /api/destinations/continent/:continent
// @access  Public
const getDestinationsByContinent = asyncHandler(async (req, res) => {
  const { continent } = req.params;
  const limit = parseInt(req.query.limit) || 20;

  const validContinents = ['Asia', 'Europe', 'North America', 'South America', 'Africa', 'Australia', 'Antarctica'];
  
  if (!validContinents.includes(continent)) {
    return res.status(400).json({
      success: false,
      message: 'القارة غير صحيحة'
    });
  }

  const destinations = await Destination.find({
    continent,
    isActive: true
  })
  .sort({ isPopular: -1, searchCount: -1 })
  .limit(limit)
  .select('city country airport.code images isPopular');

  res.status(200).json({
    success: true,
    data: {
      continent,
      destinations,
      count: destinations.length
    }
  });
});

// @desc    Create destination (Admin only)
// @route   POST /api/destinations
// @access  Private/Admin
const createDestination = asyncHandler(async (req, res) => {
  const destination = await Destination.create(req.body);

  res.status(201).json({
    success: true,
    message: 'تم إنشاء الوجهة بنجاح',
    data: {
      destination
    }
  });
});

// @desc    Update destination (Admin only)
// @route   PUT /api/destinations/:id
// @access  Private/Admin
const updateDestination = asyncHandler(async (req, res) => {
  const destination = await Destination.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  );

  if (!destination) {
    return res.status(404).json({
      success: false,
      message: 'الوجهة غير موجودة'
    });
  }

  res.status(200).json({
    success: true,
    message: 'تم تحديث الوجهة بنجاح',
    data: {
      destination
    }
  });
});

// @desc    Delete destination (Admin only)
// @route   DELETE /api/destinations/:id
// @access  Private/Admin
const deleteDestination = asyncHandler(async (req, res) => {
  const destination = await Destination.findById(req.params.id);

  if (!destination) {
    return res.status(404).json({
      success: false,
      message: 'الوجهة غير موجودة'
    });
  }

  // Soft delete by setting isActive to false
  destination.isActive = false;
  await destination.save();

  res.status(200).json({
    success: true,
    message: 'تم حذف الوجهة بنجاح'
  });
});

// @desc    Get destination statistics (Admin only)
// @route   GET /api/destinations/stats
// @access  Private/Admin
const getDestinationStats = asyncHandler(async (req, res) => {
  const stats = await Destination.aggregate([
    {
      $group: {
        _id: '$continent',
        count: { $sum: 1 },
        totalSearches: { $sum: '$searchCount' },
        totalBookings: { $sum: '$bookingCount' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  const totalDestinations = await Destination.countDocuments({ isActive: true });
  const popularDestinations = await Destination.countDocuments({ isPopular: true, isActive: true });

  res.status(200).json({
    success: true,
    data: {
      totalDestinations,
      popularDestinations,
      continentBreakdown: stats
    }
  });
});

module.exports = {
  getAllDestinations,
  getDestination,
  searchDestinations,
  getPopularDestinations,
  getDestinationsByContinent,
  createDestination,
  updateDestination,
  deleteDestination,
  getDestinationStats
};
