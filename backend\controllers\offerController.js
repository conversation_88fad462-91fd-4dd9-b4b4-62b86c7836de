const Offer = require('../models/Offer');
const Booking = require('../models/Booking');
const User = require('../models/User');
const { asyncHandler } = require('../middlewares/errorHandler');

// @desc    Get all active offers
// @route   GET /api/offers
// @access  Public
const getActiveOffers = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  // Build filter for active offers
  const now = new Date();
  const filter = {
    isActive: true,
    'validity.startDate': { $lte: now },
    'validity.endDate': { $gte: now }
  };

  if (req.query.type) {
    filter.type = req.query.type;
  }

  if (req.query.featured === 'true') {
    filter.isFeatured = true;
  }

  // Sort by priority and creation date
  const offers = await Offer.find(filter)
    .sort({ isFeatured: -1, priority: -1, createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .select('-__v');

  const total = await Offer.countDocuments(filter);

  res.status(200).json({
    success: true,
    data: {
      offers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Get offer by ID
// @route   GET /api/offers/:id
// @access  Public
const getOfferById = asyncHandler(async (req, res) => {
  const offer = await Offer.findById(req.params.id);

  if (!offer) {
    return res.status(404).json({
      success: false,
      message: 'العرض غير موجود'
    });
  }

  if (!offer.isCurrentlyValid) {
    return res.status(400).json({
      success: false,
      message: 'العرض غير نشط حالياً'
    });
  }

  // Increment click count
  offer.clickCount += 1;
  await offer.save();

  res.status(200).json({
    success: true,
    data: {
      offer
    }
  });
});

// @desc    Apply promo code
// @route   POST /api/offers/apply-promo
// @access  Private
const applyPromoCode = asyncHandler(async (req, res) => {
  const { promoCode, bookingAmount } = req.body;

  if (!promoCode || !bookingAmount) {
    return res.status(400).json({
      success: false,
      message: 'كود الخصم ومبلغ الحجز مطلوبان'
    });
  }

  // Find offer by promo code
  const offer = await Offer.findOne({
    promoCode: promoCode.toUpperCase(),
    isActive: true
  });

  if (!offer) {
    return res.status(404).json({
      success: false,
      message: 'كود الخصم غير صحيح'
    });
  }

  if (!offer.isCurrentlyValid) {
    return res.status(400).json({
      success: false,
      message: 'انتهت صلاحية كود الخصم'
    });
  }

  // Check usage limits
  if (offer.conditions.totalUsageLimit && offer.usageCount >= offer.conditions.totalUsageLimit) {
    return res.status(400).json({
      success: false,
      message: 'تم استنفاد عدد مرات استخدام هذا الكود'
    });
  }

  // Check minimum booking amount
  if (bookingAmount < offer.conditions.minBookingAmount) {
    return res.status(400).json({
      success: false,
      message: `الحد الأدنى للحجز ${offer.conditions.minBookingAmount} دولار`
    });
  }

  // Check user usage limit
  const user = await User.findById(req.user.id);
  const userBookingsWithThisOffer = await Booking.countDocuments({
    user: req.user.id,
    'appliedOffers.offerId': offer._id
  });

  if (userBookingsWithThisOffer >= offer.conditions.maxUsagePerUser) {
    return res.status(400).json({
      success: false,
      message: 'تم تجاوز الحد المسموح لاستخدام هذا الكود'
    });
  }

  // Calculate discount
  const discountAmount = offer.calculateDiscount(bookingAmount);

  res.status(200).json({
    success: true,
    message: 'تم تطبيق كود الخصم بنجاح',
    data: {
      offer: {
        id: offer._id,
        title: offer.title,
        promoCode: offer.promoCode,
        discountType: offer.discount.type,
        discountValue: offer.discount.value
      },
      originalAmount: bookingAmount,
      discountAmount,
      finalAmount: bookingAmount - discountAmount
    }
  });
});

// @desc    Get featured offers
// @route   GET /api/offers/featured
// @access  Public
const getFeaturedOffers = asyncHandler(async (req, res) => {
  const limit = parseInt(req.query.limit) || 6;

  const now = new Date();
  const offers = await Offer.find({
    isFeatured: true,
    isActive: true,
    'validity.startDate': { $lte: now },
    'validity.endDate': { $gte: now }
  })
  .sort({ priority: -1, createdAt: -1 })
  .limit(limit)
  .select('title description images discount validity type');

  res.status(200).json({
    success: true,
    data: {
      offers
    }
  });
});

// @desc    Get offers by type
// @route   GET /api/offers/type/:type
// @access  Public
const getOffersByType = asyncHandler(async (req, res) => {
  const { type } = req.params;
  const limit = parseInt(req.query.limit) || 10;

  const validTypes = ['flight-discount', 'package-deal', 'loyalty-bonus', 'seasonal', 'last-minute', 'group-booking'];
  
  if (!validTypes.includes(type)) {
    return res.status(400).json({
      success: false,
      message: 'نوع العرض غير صحيح'
    });
  }

  const now = new Date();
  const offers = await Offer.find({
    type,
    isActive: true,
    'validity.startDate': { $lte: now },
    'validity.endDate': { $gte: now }
  })
  .sort({ priority: -1, createdAt: -1 })
  .limit(limit)
  .select('title description images discount validity');

  res.status(200).json({
    success: true,
    data: {
      type,
      offers,
      count: offers.length
    }
  });
});

// @desc    Create offer (Admin only)
// @route   POST /api/offers
// @access  Private/Admin
const createOffer = asyncHandler(async (req, res) => {
  const offer = await Offer.create(req.body);

  res.status(201).json({
    success: true,
    message: 'تم إنشاء العرض بنجاح',
    data: {
      offer
    }
  });
});

// @desc    Update offer (Admin only)
// @route   PUT /api/offers/:id
// @access  Private/Admin
const updateOffer = asyncHandler(async (req, res) => {
  const offer = await Offer.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  );

  if (!offer) {
    return res.status(404).json({
      success: false,
      message: 'العرض غير موجود'
    });
  }

  res.status(200).json({
    success: true,
    message: 'تم تحديث العرض بنجاح',
    data: {
      offer
    }
  });
});

// @desc    Delete offer (Admin only)
// @route   DELETE /api/offers/:id
// @access  Private/Admin
const deleteOffer = asyncHandler(async (req, res) => {
  const offer = await Offer.findById(req.params.id);

  if (!offer) {
    return res.status(404).json({
      success: false,
      message: 'العرض غير موجود'
    });
  }

  // Soft delete by setting isActive to false
  offer.isActive = false;
  await offer.save();

  res.status(200).json({
    success: true,
    message: 'تم حذف العرض بنجاح'
  });
});

module.exports = {
  getActiveOffers,
  getOfferById,
  applyPromoCode,
  getFeaturedOffers,
  getOffersByType,
  createOffer,
  updateOffer,
  deleteOffer
};
