const mongoose = require('mongoose');

const loyaltyAccountSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  membershipNumber: {
    type: String,
    required: true,
    unique: true,
    uppercase: true
  },
  tier: {
    type: String,
    enum: ['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond'],
    default: 'Bronze'
  },
  points: {
    current: {
      type: Number,
      default: 0,
      min: 0
    },
    lifetime: {
      type: Number,
      default: 0,
      min: 0
    },
    thisYear: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  miles: {
    current: {
      type: Number,
      default: 0,
      min: 0
    },
    lifetime: {
      type: Number,
      default: 0,
      min: 0
    },
    thisYear: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  tierProgress: {
    currentTierRequirement: {
      type: Number,
      default: 0
    },
    nextTierRequirement: {
      type: Number,
      default: 5000
    },
    progressPercentage: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  },
  benefits: {
    priorityCheckin: {
      type: Boolean,
      default: false
    },
    loungeAccess: {
      type: Boolean,
      default: false
    },
    extraBaggage: {
      type: Number,
      default: 0
    },
    seatUpgrade: {
      type: Boolean,
      default: false
    },
    priorityBoarding: {
      type: Boolean,
      default: false
    },
    bonusPointsMultiplier: {
      type: Number,
      default: 1,
      min: 1
    }
  },
  transactions: [{
    type: {
      type: String,
      enum: ['earned', 'redeemed', 'expired', 'bonus', 'adjustment'],
      required: true
    },
    points: {
      type: Number,
      required: true
    },
    miles: {
      type: Number,
      default: 0
    },
    description: {
      type: String,
      required: true
    },
    booking: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Booking'
    },
    expiryDate: Date,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  achievements: [{
    name: String,
    description: String,
    earnedAt: {
      type: Date,
      default: Date.now
    },
    icon: String
  }],
  preferences: {
    seatPreference: {
      type: String,
      enum: ['window', 'aisle', 'any'],
      default: 'any'
    },
    mealPreference: {
      type: String,
      enum: ['regular', 'vegetarian', 'vegan', 'halal', 'kosher', 'gluten-free'],
      default: 'regular'
    },
    specialAssistance: [String]
  },
  isActive: {
    type: Boolean,
    default: true
  },
  joinDate: {
    type: Date,
    default: Date.now
  },
  lastActivity: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Generate membership number before saving
loyaltyAccountSchema.pre('save', function(next) {
  if (!this.membershipNumber) {
    this.membershipNumber = 'NAS' + Date.now().toString().slice(-6) + Math.random().toString(36).substr(2, 3).toUpperCase();
  }
  next();
});

// Method to calculate tier based on points/miles
loyaltyAccountSchema.methods.calculateTier = function() {
  const yearlyPoints = this.points.thisYear;
  
  if (yearlyPoints >= 50000) {
    this.tier = 'Diamond';
    this.tierProgress.nextTierRequirement = null;
  } else if (yearlyPoints >= 25000) {
    this.tier = 'Platinum';
    this.tierProgress.nextTierRequirement = 50000;
  } else if (yearlyPoints >= 15000) {
    this.tier = 'Gold';
    this.tierProgress.nextTierRequirement = 25000;
  } else if (yearlyPoints >= 5000) {
    this.tier = 'Silver';
    this.tierProgress.nextTierRequirement = 15000;
  } else {
    this.tier = 'Bronze';
    this.tierProgress.nextTierRequirement = 5000;
  }
  
  // Calculate progress percentage
  if (this.tierProgress.nextTierRequirement) {
    this.tierProgress.progressPercentage = Math.round(
      (yearlyPoints / this.tierProgress.nextTierRequirement) * 100
    );
  } else {
    this.tierProgress.progressPercentage = 100;
  }
  
  // Update benefits based on tier
  this.updateBenefits();
};

// Method to update benefits based on tier
loyaltyAccountSchema.methods.updateBenefits = function() {
  switch (this.tier) {
    case 'Diamond':
      this.benefits = {
        priorityCheckin: true,
        loungeAccess: true,
        extraBaggage: 3,
        seatUpgrade: true,
        priorityBoarding: true,
        bonusPointsMultiplier: 3
      };
      break;
    case 'Platinum':
      this.benefits = {
        priorityCheckin: true,
        loungeAccess: true,
        extraBaggage: 2,
        seatUpgrade: true,
        priorityBoarding: true,
        bonusPointsMultiplier: 2.5
      };
      break;
    case 'Gold':
      this.benefits = {
        priorityCheckin: true,
        loungeAccess: false,
        extraBaggage: 1,
        seatUpgrade: false,
        priorityBoarding: true,
        bonusPointsMultiplier: 2
      };
      break;
    case 'Silver':
      this.benefits = {
        priorityCheckin: true,
        loungeAccess: false,
        extraBaggage: 1,
        seatUpgrade: false,
        priorityBoarding: false,
        bonusPointsMultiplier: 1.5
      };
      break;
    default: // Bronze
      this.benefits = {
        priorityCheckin: false,
        loungeAccess: false,
        extraBaggage: 0,
        seatUpgrade: false,
        priorityBoarding: false,
        bonusPointsMultiplier: 1
      };
  }
};

// Index for efficient searching (user and membershipNumber already have unique indexes)
loyaltyAccountSchema.index({ tier: 1 });

module.exports = mongoose.model('LoyaltyAccount', loyaltyAccountSchema);
