const mongoose = require('mongoose');

const shippingServiceSchema = new mongoose.Schema({
  company: {
    name: {
      type: String,
      required: [true, 'اسم شركة الشحن مطلوب']
    },
    code: {
      type: String,
      required: [true, 'رمز الشركة مطلوب'],
      uppercase: true
    },
    logo: String,
    rating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    },
    trackingUrl: String
  },
  serviceTypes: [{
    name: {
      type: String,
      required: true
    },
    code: {
      type: String,
      required: true,
      uppercase: true
    },
    description: String,
    deliveryTime: {
      min: { type: Number, required: true }, // days
      max: { type: Number, required: true }
    },
    features: [{
      type: String,
      enum: ['tracking', 'insurance', 'signature-required', 'door-to-door', 'express', 'fragile-handling']
    }],
    restrictions: {
      maxWeight: { type: Number, required: true }, // kg
      maxDimensions: {
        length: Number, // cm
        width: Number,
        height: Number
      },
      prohibitedItems: [String]
    }
  }],
  coverage: {
    domestic: {
      available: { type: Boolean, default: true },
      regions: [String]
    },
    international: {
      available: { type: Boolean, default: true },
      countries: [String],
      restrictions: [String]
    }
  },
  pricing: {
    structure: {
      type: String,
      enum: ['weight-based', 'zone-based', 'flat-rate', 'dimensional'],
      default: 'weight-based'
    },
    weightRanges: [{
      min: Number, // kg
      max: Number,
      price: Number,
      currency: { type: String, default: 'USD' }
    }],
    zones: [{
      name: String,
      countries: [String],
      basePrice: Number,
      perKgPrice: Number
    }],
    additionalServices: {
      insurance: {
        rate: { type: Number, default: 0.02 }, // percentage of declared value
        minimum: { type: Number, default: 5 }
      },
      signatureRequired: { type: Number, default: 10 },
      expressDelivery: { type: Number, default: 25 },
      fragileHandling: { type: Number, default: 15 },
      packaging: {
        small: { type: Number, default: 5 },
        medium: { type: Number, default: 10 },
        large: { type: Number, default: 20 }
      }
    }
  },
  documentation: {
    required: [{
      type: String,
      enum: ['commercial-invoice', 'packing-list', 'certificate-of-origin', 'customs-declaration']
    }],
    customsInfo: {
      dutyCalculation: String,
      restrictedItems: [String],
      documentation: String
    }
  },
  tracking: {
    available: { type: Boolean, default: true },
    realTime: { type: Boolean, default: false },
    notifications: {
      sms: { type: Boolean, default: true },
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: false }
    }
  },
  insurance: {
    available: { type: Boolean, default: true },
    maxCoverage: { type: Number, default: 10000 },
    terms: String
  },
  specialServices: [{
    name: String,
    description: String,
    fee: Number,
    requirements: [String]
  }],
  operatingHours: {
    pickup: {
      weekdays: { start: String, end: String },
      weekends: { start: String, end: String }
    },
    customerService: {
      weekdays: { start: String, end: String },
      weekends: { start: String, end: String },
      phone: String,
      email: String
    }
  },
  partnerships: {
    airlines: [String],
    airports: [String],
    hotels: [String]
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isPartner: {
    type: Boolean,
    default: false
  },
  partnerDiscount: {
    type: Number,
    min: 0,
    max: 25,
    default: 0
  }
}, {
  timestamps: true
});

// Index for efficient searching
shippingServiceSchema.index({ 'company.name': 1 });
shippingServiceSchema.index({ 'serviceTypes.code': 1 });
shippingServiceSchema.index({ isPartner: -1, partnerDiscount: -1 });

module.exports = mongoose.model('ShippingService', shippingServiceSchema);
