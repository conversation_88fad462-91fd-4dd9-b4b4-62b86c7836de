import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { 
  Search, 
  Calendar, 
  Users, 
  ArrowRight, 
  ArrowLeft,
  Plane,
  MapPin,
  RotateCcw,
  Plus,
  Minus
} from 'lucide-react';

interface SearchFormData {
  tripType: 'one-way' | 'round-trip' | 'multi-city';
  from: string;
  to: string;
  departureDate: string;
  returnDate: string;
  passengers: {
    adults: number;
    children: number;
    infants: number;
  };
  class: 'economy' | 'business' | 'first';
}

const FlightSearchForm: React.FC = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [formData, setFormData] = useState<SearchFormData>({
    tripType: 'round-trip',
    from: '',
    to: '',
    departureDate: '',
    returnDate: '',
    passengers: {
      adults: 1,
      children: 0,
      infants: 0
    },
    class: 'economy'
  });

  const [showPassengers, setShowPassengers] = useState(false);

  const tripTypes = [
    { value: 'one-way', label: t('search.tripType.oneWay') },
    { value: 'round-trip', label: t('search.tripType.roundTrip') },
    { value: 'multi-city', label: t('search.tripType.multiCity') }
  ];

  const classes = [
    { value: 'economy', label: t('search.classes.economy') },
    { value: 'business', label: t('search.classes.business') },
    { value: 'first', label: t('search.classes.first') }
  ];

  const popularCities = [
    { code: 'RUH', name: isRTL ? 'الرياض' : 'Riyadh', country: isRTL ? 'السعودية' : 'Saudi Arabia' },
    { code: 'DXB', name: isRTL ? 'دبي' : 'Dubai', country: isRTL ? 'الإمارات' : 'UAE' },
    { code: 'CAI', name: isRTL ? 'القاهرة' : 'Cairo', country: isRTL ? 'مصر' : 'Egypt' },
    { code: 'LHR', name: isRTL ? 'لندن' : 'London', country: isRTL ? 'بريطانيا' : 'UK' },
    { code: 'CDG', name: isRTL ? 'باريس' : 'Paris', country: isRTL ? 'فرنسا' : 'France' },
    { code: 'JFK', name: isRTL ? 'نيويورك' : 'New York', country: isRTL ? 'أمريكا' : 'USA' }
  ];

  const updatePassengerCount = (type: 'adults' | 'children' | 'infants', increment: boolean) => {
    setFormData(prev => ({
      ...prev,
      passengers: {
        ...prev.passengers,
        [type]: increment 
          ? prev.passengers[type] + 1 
          : Math.max(type === 'adults' ? 1 : 0, prev.passengers[type] - 1)
      }
    }));
  };

  const swapCities = () => {
    setFormData(prev => ({
      ...prev,
      from: prev.to,
      to: prev.from
    }));
  };

  const getTotalPassengers = () => {
    return formData.passengers.adults + formData.passengers.children + formData.passengers.infants;
  };

  const handleSearch = () => {
    console.log('Search flights:', formData);
    // Navigate to search results
  };

  return (
    <div className="glass-effect rounded-3xl p-6 lg:p-8 shadow-large">
      {/* Trip Type Selector */}
      <div className="flex flex-wrap gap-2 mb-6">
        {tripTypes.map((type) => (
          <button
            key={type.value}
            onClick={() => setFormData(prev => ({ ...prev, tripType: type.value as any }))}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
              formData.tripType === type.value
                ? 'bg-primary-600 text-white shadow-md'
                : 'bg-white/50 text-secondary-700 hover:bg-white/70'
            }`}
          >
            {type.label}
          </button>
        ))}
      </div>

      {/* Search Form */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 mb-6">
        {/* From */}
        <div className="lg:col-span-3">
          <label className="block text-sm font-medium text-secondary-700 mb-2">
            {t('search.from')}
          </label>
          <div className="relative">
            <input
              type="text"
              value={formData.from}
              onChange={(e) => setFormData(prev => ({ ...prev, from: e.target.value }))}
              placeholder={isRTL ? 'من أين؟' : 'From where?'}
              className="input-lg pl-10 rtl:pl-4 rtl:pr-10 bg-white/80 backdrop-blur-sm"
            />
            <MapPin className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
          </div>
        </div>

        {/* Swap Button */}
        <div className="lg:col-span-1 flex items-end justify-center">
          <button
            onClick={swapCities}
            className="p-3 bg-primary-600 hover:bg-primary-700 text-white rounded-xl transition-colors duration-200 shadow-md hover:shadow-lg"
          >
            <RotateCcw className="w-5 h-5" />
          </button>
        </div>

        {/* To */}
        <div className="lg:col-span-3">
          <label className="block text-sm font-medium text-secondary-700 mb-2">
            {t('search.to')}
          </label>
          <div className="relative">
            <input
              type="text"
              value={formData.to}
              onChange={(e) => setFormData(prev => ({ ...prev, to: e.target.value }))}
              placeholder={isRTL ? 'إلى أين؟' : 'To where?'}
              className="input-lg pl-10 rtl:pl-4 rtl:pr-10 bg-white/80 backdrop-blur-sm"
            />
            <MapPin className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
          </div>
        </div>

        {/* Departure Date */}
        <div className="lg:col-span-2">
          <label className="block text-sm font-medium text-secondary-700 mb-2">
            {t('search.departure')}
          </label>
          <div className="relative">
            <input
              type="date"
              value={formData.departureDate}
              onChange={(e) => setFormData(prev => ({ ...prev, departureDate: e.target.value }))}
              className="input-lg pl-10 rtl:pl-4 rtl:pr-10 bg-white/80 backdrop-blur-sm"
            />
            <Calendar className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
          </div>
        </div>

        {/* Return Date */}
        {formData.tripType === 'round-trip' && (
          <div className="lg:col-span-2">
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              {t('search.return')}
            </label>
            <div className="relative">
              <input
                type="date"
                value={formData.returnDate}
                onChange={(e) => setFormData(prev => ({ ...prev, returnDate: e.target.value }))}
                className="input-lg pl-10 rtl:pl-4 rtl:pr-10 bg-white/80 backdrop-blur-sm"
              />
              <Calendar className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" />
            </div>
          </div>
        )}

        {/* Passengers */}
        <div className={`${formData.tripType === 'round-trip' ? 'lg:col-span-1' : 'lg:col-span-3'} relative`}>
          <label className="block text-sm font-medium text-secondary-700 mb-2">
            {t('search.passengers')}
          </label>
          <button
            onClick={() => setShowPassengers(!showPassengers)}
            className="input-lg bg-white/80 backdrop-blur-sm text-left rtl:text-right flex items-center justify-between w-full"
          >
            <span>{getTotalPassengers()} {isRTL ? 'مسافر' : 'Passenger(s)'}</span>
            <Users className="w-5 h-5 text-secondary-400" />
          </button>

          {/* Passengers Dropdown */}
          {showPassengers && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-large border border-secondary-100 p-4 z-50"
            >
              <div className="space-y-4">
                {[
                  { key: 'adults', label: t('search.adults'), min: 1 },
                  { key: 'children', label: t('search.children'), min: 0 },
                  { key: 'infants', label: t('search.infants'), min: 0 }
                ].map((passenger) => (
                  <div key={passenger.key} className="flex items-center justify-between">
                    <span className="text-sm font-medium text-secondary-700">
                      {passenger.label}
                    </span>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <button
                        onClick={() => updatePassengerCount(passenger.key as any, false)}
                        disabled={formData.passengers[passenger.key as keyof typeof formData.passengers] <= passenger.min}
                        className="w-8 h-8 rounded-full bg-secondary-100 hover:bg-secondary-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors duration-200"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <span className="w-8 text-center font-medium">
                        {formData.passengers[passenger.key as keyof typeof formData.passengers]}
                      </span>
                      <button
                        onClick={() => updatePassengerCount(passenger.key as any, true)}
                        className="w-8 h-8 rounded-full bg-secondary-100 hover:bg-secondary-200 flex items-center justify-center transition-colors duration-200"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Class and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-end">
        {/* Class Selector */}
        <div className="flex-1">
          <label className="block text-sm font-medium text-secondary-700 mb-2">
            {t('search.class')}
          </label>
          <select
            value={formData.class}
            onChange={(e) => setFormData(prev => ({ ...prev, class: e.target.value as any }))}
            className="input-lg bg-white/80 backdrop-blur-sm"
          >
            {classes.map((cls) => (
              <option key={cls.value} value={cls.value}>
                {cls.label}
              </option>
            ))}
          </select>
        </div>

        {/* Search Button */}
        <button
          onClick={handleSearch}
          className="btn-accent btn-xl px-8 py-4 flex items-center space-x-3 rtl:space-x-reverse shadow-large hover:shadow-glow min-w-fit"
        >
          <Search className="w-6 h-6" />
          <span className="font-semibold">{t('hero.searchButton')}</span>
          {isRTL ? <ArrowLeft className="w-5 h-5" /> : <ArrowRight className="w-5 h-5" />}
        </button>
      </div>

      {/* Popular Cities */}
      <div className="mt-6 pt-6 border-t border-white/20">
        <p className="text-sm font-medium text-secondary-700 mb-3">
          {isRTL ? 'وجهات شائعة:' : 'Popular destinations:'}
        </p>
        <div className="flex flex-wrap gap-2">
          {popularCities.map((city) => (
            <button
              key={city.code}
              onClick={() => setFormData(prev => ({ ...prev, to: city.name }))}
              className="px-3 py-1 bg-white/50 hover:bg-white/70 text-secondary-700 text-sm rounded-lg transition-colors duration-200"
            >
              {city.name}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FlightSearchForm;
