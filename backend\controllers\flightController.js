const Flight = require('../models/Flight');
const Destination = require('../models/Destination');
const { asyncHandler } = require('../middlewares/errorHandler');

// @desc    Search flights
// @route   POST /api/flights/search
// @access  Public
const searchFlights = asyncHandler(async (req, res) => {
  const {
    from,
    to,
    departureDate,
    returnDate,
    passengers = 1,
    class: travelClass = 'economy',
    tripType = 'one-way'
  } = req.body;

  // Build search query
  const searchQuery = {
    'route.departure.airport.code': from.toUpperCase(),
    'route.arrival.airport.code': to.toUpperCase(),
    'route.departure.scheduledTime': {
      $gte: new Date(departureDate),
      $lt: new Date(new Date(departureDate).getTime() + 24 * 60 * 60 * 1000)
    },
    isActive: true,
    status: { $in: ['scheduled', 'delayed'] }
  };

  // Check availability for the requested class
  searchQuery[`availability.${travelClass}`] = { $gte: passengers };

  // Search outbound flights
  const outboundFlights = await Flight.find(searchQuery)
    .sort({ 'route.departure.scheduledTime': 1 })
    .limit(50);

  let returnFlights = [];

  // Search return flights if round trip
  if (tripType === 'round-trip' && returnDate) {
    const returnQuery = {
      'route.departure.airport.code': to.toUpperCase(),
      'route.arrival.airport.code': from.toUpperCase(),
      'route.departure.scheduledTime': {
        $gte: new Date(returnDate),
        $lt: new Date(new Date(returnDate).getTime() + 24 * 60 * 60 * 1000)
      },
      isActive: true,
      status: { $in: ['scheduled', 'delayed'] },
      [`availability.${travelClass}`]: { $gte: passengers }
    };

    returnFlights = await Flight.find(returnQuery)
      .sort({ 'route.departure.scheduledTime': 1 })
      .limit(50);
  }

  // Update search count for destinations
  await Destination.updateOne(
    { 'airport.code': from.toUpperCase() },
    { $inc: { searchCount: 1 } }
  );
  await Destination.updateOne(
    { 'airport.code': to.toUpperCase() },
    { $inc: { searchCount: 1 } }
  );

  res.status(200).json({
    success: true,
    data: {
      searchCriteria: {
        from,
        to,
        departureDate,
        returnDate,
        passengers,
        class: travelClass,
        tripType
      },
      outboundFlights,
      returnFlights,
      totalResults: outboundFlights.length + returnFlights.length
    }
  });
});

// @desc    Get flight by ID
// @route   GET /api/flights/:id
// @access  Public
const getFlightById = asyncHandler(async (req, res) => {
  const flight = await Flight.findById(req.params.id);

  if (!flight) {
    return res.status(404).json({
      success: false,
      message: 'الرحلة غير موجودة'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      flight
    }
  });
});

// @desc    Get all flights (admin)
// @route   GET /api/flights
// @access  Private/Admin
const getAllFlights = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;

  // Build filter
  const filter = {};
  
  if (req.query.status) {
    filter.status = req.query.status;
  }
  
  if (req.query.airline) {
    filter['airline.code'] = req.query.airline.toUpperCase();
  }
  
  if (req.query.from) {
    filter['route.departure.airport.code'] = req.query.from.toUpperCase();
  }
  
  if (req.query.to) {
    filter['route.arrival.airport.code'] = req.query.to.toUpperCase();
  }

  const flights = await Flight.find(filter)
    .sort({ 'route.departure.scheduledTime': 1 })
    .skip(skip)
    .limit(limit);

  const total = await Flight.countDocuments(filter);

  res.status(200).json({
    success: true,
    data: {
      flights,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Create flight (admin)
// @route   POST /api/flights
// @access  Private/Admin
const createFlight = asyncHandler(async (req, res) => {
  const flight = await Flight.create(req.body);

  res.status(201).json({
    success: true,
    message: 'تم إنشاء الرحلة بنجاح',
    data: {
      flight
    }
  });
});

// @desc    Update flight (admin)
// @route   PUT /api/flights/:id
// @access  Private/Admin
const updateFlight = asyncHandler(async (req, res) => {
  const flight = await Flight.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  );

  if (!flight) {
    return res.status(404).json({
      success: false,
      message: 'الرحلة غير موجودة'
    });
  }

  res.status(200).json({
    success: true,
    message: 'تم تحديث الرحلة بنجاح',
    data: {
      flight
    }
  });
});

// @desc    Delete flight (admin)
// @route   DELETE /api/flights/:id
// @access  Private/Admin
const deleteFlight = asyncHandler(async (req, res) => {
  const flight = await Flight.findById(req.params.id);

  if (!flight) {
    return res.status(404).json({
      success: false,
      message: 'الرحلة غير موجودة'
    });
  }

  await flight.deleteOne();

  res.status(200).json({
    success: true,
    message: 'تم حذف الرحلة بنجاح'
  });
});

// @desc    Update flight status
// @route   PATCH /api/flights/:id/status
// @access  Private/Admin
const updateFlightStatus = asyncHandler(async (req, res) => {
  const { status } = req.body;

  const validStatuses = ['scheduled', 'delayed', 'cancelled', 'boarding', 'departed', 'arrived'];

  if (!validStatuses.includes(status)) {
    return res.status(400).json({
      success: false,
      message: 'حالة الرحلة غير صحيحة'
    });
  }

  const flight = await Flight.findByIdAndUpdate(
    req.params.id,
    { status },
    { new: true, runValidators: true }
  );

  if (!flight) {
    return res.status(404).json({
      success: false,
      message: 'الرحلة غير موجودة'
    });
  }

  res.status(200).json({
    success: true,
    message: 'تم تحديث حالة الرحلة بنجاح',
    data: {
      flight
    }
  });
});

// @desc    Get flights by route
// @route   GET /api/flights/route/:from/:to
// @access  Public
const getFlightsByRoute = asyncHandler(async (req, res) => {
  const { from, to } = req.params;
  const { date, days = 7 } = req.query;

  let dateFilter = {};

  if (date) {
    const searchDate = new Date(date);
    const nextDay = new Date(searchDate);
    nextDay.setDate(nextDay.getDate() + parseInt(days));

    dateFilter = {
      'route.departure.scheduledTime': {
        $gte: searchDate,
        $lt: nextDay
      }
    };
  }

  const flights = await Flight.find({
    'route.departure.airport.code': from.toUpperCase(),
    'route.arrival.airport.code': to.toUpperCase(),
    isActive: true,
    status: { $in: ['scheduled', 'delayed'] },
    ...dateFilter
  })
  .sort({ 'route.departure.scheduledTime': 1 })
  .limit(50);

  res.status(200).json({
    success: true,
    data: {
      route: { from: from.toUpperCase(), to: to.toUpperCase() },
      flights,
      count: flights.length
    }
  });
});

// @desc    Get flight statistics
// @route   GET /api/flights/stats
// @access  Private/Admin
const getFlightStats = asyncHandler(async (req, res) => {
  const stats = await Flight.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);

  const airlineStats = await Flight.aggregate([
    {
      $group: {
        _id: '$airline.code',
        name: { $first: '$airline.name' },
        count: { $sum: 1 },
        avgPrice: { $avg: '$pricing.economy.totalPrice' }
      }
    },
    { $sort: { count: -1 } },
    { $limit: 10 }
  ]);

  const totalFlights = await Flight.countDocuments();
  const activeFlights = await Flight.countDocuments({ isActive: true });
  const todayFlights = await Flight.countDocuments({
    'route.departure.scheduledTime': {
      $gte: new Date().setHours(0, 0, 0, 0),
      $lt: new Date().setHours(23, 59, 59, 999)
    }
  });

  res.status(200).json({
    success: true,
    data: {
      totalFlights,
      activeFlights,
      todayFlights,
      statusBreakdown: stats,
      topAirlines: airlineStats
    }
  });
});

module.exports = {
  searchFlights,
  getFlightById,
  getAllFlights,
  createFlight,
  updateFlight,
  deleteFlight,
  updateFlightStatus,
  getFlightsByRoute,
  getFlightStats
};
