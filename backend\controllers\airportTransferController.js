const AirportTransfer = require('../models/AirportTransfer');
const Destination = require('../models/Destination');
const { asyncHandler } = require('../middlewares/errorHandler');

// @desc    Search airport transfers
// @route   POST /api/airport-transfers/search
// @access  Public
const searchAirportTransfers = asyncHandler(async (req, res) => {
  const {
    destination,
    transferType = 'arrival', // arrival or departure
    passengers = 1,
    serviceType,
    maxPrice
  } = req.body;

  // Find destination
  const destinationDoc = await Destination.findOne({
    $or: [
      { city: new RegExp(destination, 'i') },
      { 'airport.code': destination.toUpperCase() }
    ]
  });

  if (!destinationDoc) {
    return res.status(404).json({
      success: false,
      message: 'الوجهة غير موجودة'
    });
  }

  // Build search query
  const searchQuery = {
    destination: destinationDoc._id,
    isActive: true
  };

  let transfers = await AirportTransfer.find(searchQuery)
    .populate('destination', 'city country airport.code');

  // Filter services based on criteria
  const filteredTransfers = transfers.map(transfer => {
    let availableServices = transfer.services.filter(service => {
      let matches = true;
      
      if (serviceType) {
        matches = matches && service.type === serviceType;
      }
      
      if (passengers) {
        matches = matches && service.availability.maxPassengers >= passengers;
      }
      
      if (maxPrice) {
        matches = matches && service.pricing.basePrice <= maxPrice;
      }
      
      return matches;
    });

    return {
      ...transfer.toObject(),
      availableServices,
      hasAvailability: availableServices.length > 0
    };
  }).filter(transfer => transfer.hasAvailability);

  res.status(200).json({
    success: true,
    data: {
      searchCriteria: {
        destination: destinationDoc.city,
        transferType,
        passengers,
        serviceType,
        maxPrice
      },
      transfers: filteredTransfers,
      totalResults: filteredTransfers.length
    }
  });
});

// @desc    Get airport transfer by ID
// @route   GET /api/airport-transfers/:id
// @access  Public
const getAirportTransferById = asyncHandler(async (req, res) => {
  const transfer = await AirportTransfer.findById(req.params.id)
    .populate('destination', 'city country airport.code timezone currency');

  if (!transfer) {
    return res.status(404).json({
      success: false,
      message: 'خدمة النقل غير موجودة'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      transfer
    }
  });
});

// @desc    Get transfers by destination
// @route   GET /api/airport-transfers/destination/:destinationId
// @access  Public
const getTransfersByDestination = asyncHandler(async (req, res) => {
  const { destinationId } = req.params;
  const limit = parseInt(req.query.limit) || 10;

  const transfers = await AirportTransfer.find({
    destination: destinationId,
    isActive: true
  })
  .populate('destination', 'city country')
  .sort({ 'provider.rating': -1, isPartner: -1 })
  .limit(limit);

  res.status(200).json({
    success: true,
    data: {
      transfers,
      count: transfers.length
    }
  });
});

// @desc    Get partner transfers
// @route   GET /api/airport-transfers/partners
// @access  Public
const getPartnerTransfers = asyncHandler(async (req, res) => {
  const limit = parseInt(req.query.limit) || 8;

  const transfers = await AirportTransfer.find({
    isPartner: true,
    isActive: true
  })
  .populate('destination', 'city country')
  .sort({ partnerDiscount: -1, 'provider.rating': -1 })
  .limit(limit)
  .select('provider destination partnerDiscount services.0');

  res.status(200).json({
    success: true,
    data: {
      transfers
    }
  });
});

// @desc    Get transfer service types
// @route   GET /api/airport-transfers/service-types
// @access  Public
const getServiceTypes = asyncHandler(async (req, res) => {
  const serviceTypes = [
    {
      code: 'private-car',
      name: { ar: 'سيارة خاصة', en: 'Private Car' },
      description: { ar: 'سيارة خاصة مع سائق', en: 'Private car with driver' },
      features: ['privacy', 'comfort', 'direct-route', 'flexible-timing'],
      capacity: { min: 1, max: 4 },
      priceRange: { min: 30, max: 80 }
    },
    {
      code: 'shared-shuttle',
      name: { ar: 'حافلة مشتركة', en: 'Shared Shuttle' },
      description: { ar: 'حافلة مشتركة مع مسافرين آخرين', en: 'Shared shuttle with other passengers' },
      features: ['economical', 'scheduled', 'multiple-stops'],
      capacity: { min: 1, max: 12 },
      priceRange: { min: 10, max: 25 }
    },
    {
      code: 'luxury-car',
      name: { ar: 'سيارة فاخرة', en: 'Luxury Car' },
      description: { ar: 'سيارة فاخرة مع خدمة مميزة', en: 'Luxury car with premium service' },
      features: ['luxury', 'premium-service', 'comfort', 'prestige'],
      capacity: { min: 1, max: 4 },
      priceRange: { min: 60, max: 150 }
    },
    {
      code: 'van',
      name: { ar: 'فان', en: 'Van' },
      description: { ar: 'فان واسع للمجموعات', en: 'Spacious van for groups' },
      features: ['spacious', 'group-friendly', 'luggage-space'],
      capacity: { min: 5, max: 8 },
      priceRange: { min: 50, max: 100 }
    },
    {
      code: 'bus',
      name: { ar: 'حافلة', en: 'Bus' },
      description: { ar: 'حافلة للمجموعات الكبيرة', en: 'Bus for large groups' },
      features: ['economical', 'large-groups', 'scheduled'],
      capacity: { min: 10, max: 50 },
      priceRange: { min: 5, max: 15 }
    }
  ];

  res.status(200).json({
    success: true,
    data: {
      serviceTypes
    }
  });
});

// @desc    Calculate transfer price
// @route   POST /api/airport-transfers/calculate-price
// @access  Public
const calculateTransferPrice = asyncHandler(async (req, res) => {
  const {
    transferId,
    serviceType,
    passengers,
    extraStops = 0,
    waitingTime = 0,
    extras = []
  } = req.body;

  const transfer = await AirportTransfer.findById(transferId);

  if (!transfer) {
    return res.status(404).json({
      success: false,
      message: 'خدمة النقل غير موجودة'
    });
  }

  const service = transfer.services.find(s => s.type === serviceType);

  if (!service) {
    return res.status(404).json({
      success: false,
      message: 'نوع الخدمة غير متوفر'
    });
  }

  let totalPrice = service.pricing.basePrice;

  // Calculate additional passenger fees
  if (service.pricing.priceType === 'per-person') {
    totalPrice = totalPrice * passengers;
  } else if (passengers > 1 && service.pricing.additionalPassengerFee > 0) {
    totalPrice += (passengers - 1) * service.pricing.additionalPassengerFee;
  }

  // Add extra stops fee
  if (extraStops > 0) {
    totalPrice += extraStops * service.pricing.extraStopFee;
  }

  // Add waiting time fee
  if (waitingTime > 0) {
    totalPrice += Math.ceil(waitingTime / 60) * service.pricing.waitingTimeFee;
  }

  // Add extras
  let extrasTotal = 0;
  extras.forEach(extra => {
    if (extra === 'child-seat') extrasTotal += 10;
    if (extra === 'wifi') extrasTotal += 5;
    if (extra === 'water') extrasTotal += 3;
  });

  totalPrice += extrasTotal;

  // Apply partner discount if applicable
  if (transfer.isPartner && transfer.partnerDiscount > 0) {
    const discount = (totalPrice * transfer.partnerDiscount) / 100;
    totalPrice -= discount;
  }

  res.status(200).json({
    success: true,
    data: {
      basePrice: service.pricing.basePrice,
      additionalFees: {
        extraPassengers: passengers > 1 ? (passengers - 1) * service.pricing.additionalPassengerFee : 0,
        extraStops: extraStops * service.pricing.extraStopFee,
        waitingTime: Math.ceil(waitingTime / 60) * service.pricing.waitingTimeFee,
        extras: extrasTotal
      },
      discount: transfer.isPartner ? (service.pricing.basePrice * transfer.partnerDiscount) / 100 : 0,
      totalPrice: Math.round(totalPrice * 100) / 100,
      currency: service.pricing.currency
    }
  });
});

// @desc    Create airport transfer (Admin only)
// @route   POST /api/airport-transfers
// @access  Private/Admin
const createAirportTransfer = asyncHandler(async (req, res) => {
  const transfer = await AirportTransfer.create(req.body);

  res.status(201).json({
    success: true,
    message: 'تم إنشاء خدمة النقل بنجاح',
    data: {
      transfer
    }
  });
});

// @desc    Update airport transfer (Admin only)
// @route   PUT /api/airport-transfers/:id
// @access  Private/Admin
const updateAirportTransfer = asyncHandler(async (req, res) => {
  const transfer = await AirportTransfer.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  );

  if (!transfer) {
    return res.status(404).json({
      success: false,
      message: 'خدمة النقل غير موجودة'
    });
  }

  res.status(200).json({
    success: true,
    message: 'تم تحديث خدمة النقل بنجاح',
    data: {
      transfer
    }
  });
});

// @desc    Delete airport transfer (Admin only)
// @route   DELETE /api/airport-transfers/:id
// @access  Private/Admin
const deleteAirportTransfer = asyncHandler(async (req, res) => {
  const transfer = await AirportTransfer.findById(req.params.id);

  if (!transfer) {
    return res.status(404).json({
      success: false,
      message: 'خدمة النقل غير موجودة'
    });
  }

  transfer.isActive = false;
  await transfer.save();

  res.status(200).json({
    success: true,
    message: 'تم حذف خدمة النقل بنجاح'
  });
});

module.exports = {
  searchAirportTransfers,
  getAirportTransferById,
  getTransfersByDestination,
  getPartnerTransfers,
  getServiceTypes,
  calculateTransferPrice,
  createAirportTransfer,
  updateAirportTransfer,
  deleteAirportTransfer
};
