<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlyWay - طرق السماء | React App</title>
    
    <!-- React CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <style>
        /* Custom animations */
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        
        .animate-bounce-soft {
            animation: bounce-soft 3s ease-in-out infinite;
        }
        
        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }
        
        .animate-fade-in-down {
            animation: fadeInDown 0.3s ease-out forwards;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        @keyframes bounce-soft {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-accent {
            background: linear-gradient(135deg, #f59e0b, #ea580c);
            transition: all 0.3s ease;
        }
        
        .btn-accent:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Translation hook simulation
        const useTranslation = () => {
            const translations = {
                'hero.title': 'اكتشف العالم معنا',
                'hero.subtitle': 'رحلات استثنائية إلى أجمل الوجهات حول العالم بأفضل الأسعار والخدمات',
                'hero.popularDestinations': 'الوجهات الشائعة',
                'hero.searchButton': 'ابحث عن الرحلات',
                'search.from': 'من',
                'search.to': 'إلى',
                'search.departure': 'تاريخ المغادرة',
                'search.return': 'تاريخ العودة',
                'search.passengers': 'المسافرون',
                'search.class': 'الدرجة',
                'search.tripType.oneWay': 'ذهاب فقط',
                'search.tripType.roundTrip': 'ذهاب وعودة',
                'search.tripType.multiCity': 'متعدد المدن',
                'search.classes.economy': 'اقتصادية',
                'search.classes.business': 'رجال أعمال',
                'search.classes.first': 'أولى'
            };
            
            return {
                t: (key) => translations[key] || key,
                i18n: { language: 'ar' }
            };
        };

        // Flight Search Form Component
        const FlightSearchForm = () => {
            const { t } = useTranslation();
            const [formData, setFormData] = useState({
                tripType: 'round-trip',
                from: '',
                to: '',
                departureDate: '',
                returnDate: '',
                passengers: { adults: 1, children: 0, infants: 0 },
                class: 'economy'
            });

            const [showPassengers, setShowPassengers] = useState(false);

            const tripTypes = [
                { value: 'one-way', label: t('search.tripType.oneWay') },
                { value: 'round-trip', label: t('search.tripType.roundTrip') },
                { value: 'multi-city', label: t('search.tripType.multiCity') }
            ];

            const classes = [
                { value: 'economy', label: t('search.classes.economy') },
                { value: 'business', label: t('search.classes.business') },
                { value: 'first', label: t('search.classes.first') }
            ];

            const getTotalPassengers = () => {
                return formData.passengers.adults + formData.passengers.children + formData.passengers.infants;
            };

            const handleSearch = () => {
                console.log('Search flights:', formData);
                alert('تم البحث عن الرحلات! (هذا مجرد عرض توضيحي)');
            };

            return (
                <div className="glass-effect rounded-3xl p-6 lg:p-8 shadow-2xl">
                    {/* Trip Type Selector */}
                    <div className="flex flex-wrap gap-2 mb-6">
                        {tripTypes.map((type) => (
                            <button
                                key={type.value}
                                onClick={() => setFormData(prev => ({ ...prev, tripType: type.value }))}
                                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                    formData.tripType === type.value
                                        ? 'bg-blue-600 text-white shadow-md'
                                        : 'bg-white/50 text-gray-700 hover:bg-white/70'
                                }`}
                            >
                                {type.label}
                            </button>
                        ))}
                    </div>

                    {/* Search Form */}
                    <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 mb-6">
                        {/* From */}
                        <div className="lg:col-span-3">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('search.from')}
                            </label>
                            <input
                                type="text"
                                value={formData.from}
                                onChange={(e) => setFormData(prev => ({ ...prev, from: e.target.value }))}
                                placeholder="من أين؟"
                                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80"
                            />
                        </div>

                        {/* Swap Button */}
                        <div className="lg:col-span-1 flex items-end justify-center">
                            <button
                                onClick={() => setFormData(prev => ({ ...prev, from: prev.to, to: prev.from }))}
                                className="p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-colors duration-200 shadow-md hover:shadow-lg"
                            >
                                ⟲
                            </button>
                        </div>

                        {/* To */}
                        <div className="lg:col-span-3">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('search.to')}
                            </label>
                            <input
                                type="text"
                                value={formData.to}
                                onChange={(e) => setFormData(prev => ({ ...prev, to: e.target.value }))}
                                placeholder="إلى أين؟"
                                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80"
                            />
                        </div>

                        {/* Departure Date */}
                        <div className="lg:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('search.departure')}
                            </label>
                            <input
                                type="date"
                                value={formData.departureDate}
                                onChange={(e) => setFormData(prev => ({ ...prev, departureDate: e.target.value }))}
                                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80"
                            />
                        </div>

                        {/* Return Date */}
                        {formData.tripType === 'round-trip' && (
                            <div className="lg:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('search.return')}
                                </label>
                                <input
                                    type="date"
                                    value={formData.returnDate}
                                    onChange={(e) => setFormData(prev => ({ ...prev, returnDate: e.target.value }))}
                                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80"
                                />
                            </div>
                        )}

                        {/* Passengers */}
                        <div className={`${formData.tripType === 'round-trip' ? 'lg:col-span-1' : 'lg:col-span-3'} relative`}>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('search.passengers')}
                            </label>
                            <button
                                onClick={() => setShowPassengers(!showPassengers)}
                                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80 text-right flex items-center justify-between"
                            >
                                <span>{getTotalPassengers()} مسافر</span>
                                <span>👥</span>
                            </button>
                        </div>
                    </div>

                    {/* Class and Search */}
                    <div className="flex flex-col sm:flex-row gap-4 items-end">
                        {/* Class Selector */}
                        <div className="flex-1">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('search.class')}
                            </label>
                            <select
                                value={formData.class}
                                onChange={(e) => setFormData(prev => ({ ...prev, class: e.target.value }))}
                                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80"
                            >
                                {classes.map((cls) => (
                                    <option key={cls.value} value={cls.value}>
                                        {cls.label}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Search Button */}
                        <button
                            onClick={handleSearch}
                            className="btn-accent text-white px-8 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 min-w-fit"
                        >
                            🔍 {t('hero.searchButton')}
                        </button>
                    </div>

                    {/* Popular Cities */}
                    <div className="mt-6 pt-6 border-t border-white/20">
                        <p className="text-sm font-medium text-gray-700 mb-3">
                            وجهات شائعة:
                        </p>
                        <div className="flex flex-wrap gap-2">
                            {['الرياض', 'دبي', 'القاهرة', 'لندن', 'باريس', 'نيويورك'].map((city) => (
                                <button
                                    key={city}
                                    onClick={() => setFormData(prev => ({ ...prev, to: city }))}
                                    className="px-3 py-1 bg-white/50 hover:bg-white/70 text-gray-700 text-sm rounded-lg transition-colors duration-200"
                                >
                                    {city}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            );
        };

        // Hero Section Component
        const HeroSection = () => {
            const { t } = useTranslation();

            const popularDestinations = [
                {
                    city: 'دبي',
                    country: 'الإمارات',
                    image: 'https://images.unsplash.com/photo-1512453979798-5ea266f8880c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    price: '$299',
                    rating: 4.8
                },
                {
                    city: 'لندن',
                    country: 'بريطانيا',
                    image: 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    price: '$599',
                    rating: 4.9
                },
                {
                    city: 'باريس',
                    country: 'فرنسا',
                    image: 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    price: '$549',
                    rating: 4.7
                },
                {
                    city: 'طوكيو',
                    country: 'اليابان',
                    image: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    price: '$799',
                    rating: 4.8
                }
            ];

            return (
                <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
                    {/* Background Image with Overlay */}
                    <div className="absolute inset-0 z-0">
                        <img
                            src="https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                            alt="Hero Background"
                            className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 via-blue-800/70 to-orange-600/60"></div>
                        <div className="absolute inset-0 bg-black/20"></div>
                    </div>

                    {/* Floating Elements */}
                    <div className="absolute inset-0 z-10">
                        <div className="absolute top-20 left-10 w-16 h-16 bg-white/10 rounded-full backdrop-blur-sm animate-float"></div>
                        <div className="absolute top-40 right-20 w-12 h-12 bg-orange-400/20 rounded-full backdrop-blur-sm animate-bounce-soft"></div>
                        <div className="absolute bottom-40 left-20 w-20 h-20 bg-blue-400/15 rounded-full backdrop-blur-sm animate-float"></div>
                    </div>

                    {/* Main Content */}
                    <div className="relative z-20 container mx-auto px-4">
                        <div className="text-center mb-12">
                            {/* Hero Title */}
                            <div className="mb-8 animate-fade-in-up">
                                <h1 className="text-4xl md:text-5xl lg:text-7xl font-bold text-white leading-tight mb-6">
                                    <span className="block">{t('hero.title')}</span>
                                    <span className="block text-orange-300">طرق السماء</span>
                                </h1>
                                
                                <p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                                    {t('hero.subtitle')}
                                </p>
                            </div>

                            {/* Stats */}
                            <div className="flex flex-wrap justify-center gap-8 mb-12 animate-fade-in-up">
                                {[
                                    { number: '15M+', label: 'مسافر سنوياً' },
                                    { number: '85+', label: 'وجهة' },
                                    { number: '120+', label: 'طائرة' },
                                    { number: '96%', label: 'رضا العملاء' }
                                ].map((stat, index) => (
                                    <div key={index} className="text-center">
                                        <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                                            {stat.number}
                                        </div>
                                        <div className="text-white/80 text-sm md:text-base">
                                            {stat.label}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Flight Search Form */}
                        <div className="max-w-6xl mx-auto animate-fade-in-up">
                            <FlightSearchForm />
                        </div>

                        {/* Popular Destinations */}
                        <div className="mt-16 animate-fade-in-up">
                            <h3 className="text-2xl md:text-3xl font-bold text-white text-center mb-8">
                                {t('hero.popularDestinations')}
                            </h3>
                            
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                                {popularDestinations.map((destination, index) => (
                                    <div
                                        key={index}
                                        className="group cursor-pointer animate-fade-in-up"
                                        style={{ animationDelay: `${index * 0.1}s` }}
                                    >
                                        <div className="relative overflow-hidden rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 hover:-translate-y-2">
                                            <div className="aspect-[4/3] overflow-hidden">
                                                <img
                                                    src={destination.image}
                                                    alt={destination.city}
                                                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                                />
                                            </div>
                                            <div className="p-4">
                                                <div className="flex items-center justify-between mb-2">
                                                    <div>
                                                        <h4 className="text-lg font-semibold text-white">
                                                            {destination.city}
                                                        </h4>
                                                        <p className="text-white/70 text-sm">
                                                            {destination.country}
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center space-x-1">
                                                        <span className="text-yellow-400">⭐</span>
                                                        <span className="text-white text-sm">{destination.rating}</span>
                                                    </div>
                                                </div>
                                                <div className="flex items-center justify-between">
                                                    <span className="text-orange-300 font-bold text-lg">
                                                        من {destination.price}
                                                    </span>
                                                    <button className="flex items-center space-x-1 text-white/80 hover:text-white transition-colors duration-200">
                                                        <span className="text-sm">عرض</span>
                                                        <span>←</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* CTA Buttons */}
                        <div className="flex flex-col sm:flex-row gap-4 justify-center mt-12 animate-fade-in-up">
                            <button className="btn-accent text-white px-8 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                                ✈️ احجز رحلتك الآن
                            </button>
                            <button className="border-2 border-white text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                                🗺️ استكشف الوجهات
                            </button>
                        </div>
                    </div>

                    {/* Scroll Indicator */}
                    <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 animate-bounce">
                        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
                            <div className="w-1 h-3 bg-white/70 rounded-full mt-2"></div>
                        </div>
                    </div>
                </section>
            );
        };

        // Main App Component
        const App = () => {
            return (
                <div className="min-h-screen">
                    <HeroSection />
                </div>
            );
        };

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
