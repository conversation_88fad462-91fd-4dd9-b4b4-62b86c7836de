const mongoose = require('mongoose');

const airportTransferSchema = new mongoose.Schema({
  destination: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Destination',
    required: true
  },
  provider: {
    name: {
      type: String,
      required: [true, 'اسم مقدم الخدمة مطلوب']
    },
    code: {
      type: String,
      required: [true, 'رمز مقدم الخدمة مطلوب'],
      uppercase: true
    },
    logo: String,
    rating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    },
    reviewCount: {
      type: Number,
      default: 0
    }
  },
  services: [{
    type: {
      type: String,
      enum: ['private-car', 'shared-shuttle', 'luxury-car', 'van', 'bus', 'taxi'],
      required: true
    },
    name: {
      type: String,
      required: true
    },
    description: String,
    vehicle: {
      model: String,
      capacity: {
        passengers: { type: Number, required: true },
        luggage: { type: Number, required: true }
      },
      features: [{
        type: String,
        enum: ['air-conditioning', 'wifi', 'water', 'phone-charger', 'child-seat', 'wheelchair-accessible']
      }],
      images: [String]
    },
    pricing: {
      basePrice: { type: Number, required: true },
      currency: { type: String, default: 'USD' },
      priceType: {
        type: String,
        enum: ['per-vehicle', 'per-person'],
        default: 'per-vehicle'
      },
      additionalPassengerFee: { type: Number, default: 0 },
      waitingTimeFee: { type: Number, default: 0 }, // per hour
      extraStopFee: { type: Number, default: 0 }
    },
    duration: {
      estimated: { type: Number, required: true }, // minutes
      traffic: {
        peak: Number,
        offPeak: Number
      }
    },
    availability: {
      schedule: {
        type: String,
        enum: ['24/7', 'limited', 'on-demand'],
        default: '24/7'
      },
      advanceBooking: {
        required: { type: Boolean, default: false },
        minimumHours: { type: Number, default: 2 }
      },
      maxPassengers: { type: Number, required: true }
    }
  }],
  coverage: {
    airports: [{
      code: String,
      name: String,
      terminals: [String]
    }],
    areas: [{
      name: String,
      type: {
        type: String,
        enum: ['city-center', 'hotel-zone', 'residential', 'business', 'tourist']
      },
      surcharge: { type: Number, default: 0 }
    }],
    hotels: [{
      name: String,
      address: String,
      coordinates: {
        latitude: Number,
        longitude: Number
      }
    }]
  },
  policies: {
    cancellation: {
      freeCancellation: { type: Number, default: 24 }, // hours before pickup
      cancellationFee: { type: Number, default: 0 }
    },
    waitingTime: {
      included: { type: Number, default: 60 }, // minutes
      additionalFee: { type: Number, default: 10 } // per 15 minutes
    },
    luggage: {
      included: { type: Number, default: 2 }, // pieces per passenger
      oversizeFee: { type: Number, default: 15 },
      extraBagFee: { type: Number, default: 10 }
    },
    payment: {
      methods: [{
        type: String,
        enum: ['cash', 'credit-card', 'online', 'paypal']
      }],
      advancePayment: { type: Boolean, default: false }
    }
  },
  contact: {
    phone: String,
    whatsapp: String,
    email: String,
    website: String,
    emergencyPhone: String
  },
  operatingHours: {
    type: String,
    enum: ['24/7', 'business-hours', 'custom'],
    default: '24/7'
  },
  customHours: {
    weekdays: {
      start: String,
      end: String
    },
    weekends: {
      start: String,
      end: String
    }
  },
  specialOffers: [{
    name: String,
    description: String,
    discount: {
      type: String,
      enum: ['percentage', 'fixed-amount']
    },
    value: Number,
    validUntil: Date,
    conditions: String
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  isPartner: {
    type: Boolean,
    default: false
  },
  partnerDiscount: {
    type: Number,
    min: 0,
    max: 30,
    default: 0
  }
}, {
  timestamps: true
});

// Index for efficient searching
airportTransferSchema.index({ destination: 1, isActive: 1 });
airportTransferSchema.index({ 'provider.name': 1 });
airportTransferSchema.index({ 'services.type': 1 });
airportTransferSchema.index({ isPartner: -1, partnerDiscount: -1 });

module.exports = mongoose.model('AirportTransfer', airportTransferSchema);
