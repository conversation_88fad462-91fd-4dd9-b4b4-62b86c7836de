const Hotel = require('../models/Hotel');
const Destination = require('../models/Destination');
const { asyncHandler } = require('../middlewares/errorHandler');

// @desc    Search hotels
// @route   POST /api/hotels/search
// @access  Public
const searchHotels = asyncHandler(async (req, res) => {
  const {
    destination,
    checkIn,
    checkOut,
    guests = 1,
    rooms = 1,
    starRating,
    priceRange,
    amenities
  } = req.body;

  // Build search query
  const searchQuery = {
    isActive: true
  };

  // Search by destination (city or airport code)
  if (destination) {
    const destinationDoc = await Destination.findOne({
      $or: [
        { city: new RegExp(destination, 'i') },
        { 'airport.code': destination.toUpperCase() }
      ]
    });

    if (destinationDoc) {
      searchQuery.destination = destinationDoc._id;
    } else {
      return res.status(404).json({
        success: false,
        message: 'الوجهة غير موجودة'
      });
    }
  }

  // Filter by star rating
  if (starRating) {
    searchQuery.starRating = { $gte: parseInt(starRating) };
  }

  // Filter by amenities
  if (amenities && amenities.length > 0) {
    searchQuery.amenities = { $in: amenities };
  }

  // Filter by partner hotels
  if (req.query.partnersOnly === 'true') {
    searchQuery.isPartner = true;
  }

  let hotels = await Hotel.find(searchQuery)
    .populate('destination', 'city country airport.code')
    .sort({ 'rating.average': -1, starRating: -1 });

  // Filter by price range if specified
  if (priceRange && priceRange.min !== undefined && priceRange.max !== undefined) {
    hotels = hotels.filter(hotel => {
      const hasRoomInRange = hotel.roomTypes.some(room => 
        room.pricing.basePrice >= priceRange.min && 
        room.pricing.basePrice <= priceRange.max
      );
      return hasRoomInRange;
    });
  }

  // Calculate availability for each hotel (simplified)
  const hotelsWithAvailability = hotels.map(hotel => {
    const availableRooms = hotel.roomTypes.filter(room => 
      room.availability >= rooms && 
      room.capacity.adults >= guests
    );

    return {
      ...hotel.toObject(),
      availableRooms,
      hasAvailability: availableRooms.length > 0,
      lowestPrice: availableRooms.length > 0 ? 
        Math.min(...availableRooms.map(room => room.pricing.basePrice)) : null
    };
  });

  // Filter hotels with availability
  const availableHotels = hotelsWithAvailability.filter(hotel => hotel.hasAvailability);

  res.status(200).json({
    success: true,
    data: {
      searchCriteria: {
        destination,
        checkIn,
        checkOut,
        guests,
        rooms
      },
      hotels: availableHotels,
      totalResults: availableHotels.length
    }
  });
});

// @desc    Get hotel by ID
// @route   GET /api/hotels/:id
// @access  Public
const getHotelById = asyncHandler(async (req, res) => {
  const hotel = await Hotel.findById(req.params.id)
    .populate('destination', 'city country airport.code timezone currency');

  if (!hotel) {
    return res.status(404).json({
      success: false,
      message: 'الفندق غير موجود'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      hotel
    }
  });
});

// @desc    Get hotels by destination
// @route   GET /api/hotels/destination/:destinationId
// @access  Public
const getHotelsByDestination = asyncHandler(async (req, res) => {
  const { destinationId } = req.params;
  const limit = parseInt(req.query.limit) || 20;
  const sortBy = req.query.sortBy || 'rating'; // rating, price, stars

  let sortOption = {};
  switch (sortBy) {
    case 'price':
      sortOption = { 'roomTypes.0.pricing.basePrice': 1 };
      break;
    case 'stars':
      sortOption = { starRating: -1 };
      break;
    default:
      sortOption = { 'rating.average': -1 };
  }

  const hotels = await Hotel.find({
    destination: destinationId,
    isActive: true
  })
  .populate('destination', 'city country')
  .sort(sortOption)
  .limit(limit);

  res.status(200).json({
    success: true,
    data: {
      hotels,
      count: hotels.length
    }
  });
});

// @desc    Get partner hotels
// @route   GET /api/hotels/partners
// @access  Public
const getPartnerHotels = asyncHandler(async (req, res) => {
  const limit = parseInt(req.query.limit) || 12;

  const hotels = await Hotel.find({
    isPartner: true,
    isActive: true
  })
  .populate('destination', 'city country')
  .sort({ partnerDiscount: -1, 'rating.average': -1 })
  .limit(limit)
  .select('name destination starRating rating partnerDiscount images');

  res.status(200).json({
    success: true,
    data: {
      hotels
    }
  });
});

// @desc    Create hotel (Admin only)
// @route   POST /api/hotels
// @access  Private/Admin
const createHotel = asyncHandler(async (req, res) => {
  const hotel = await Hotel.create(req.body);

  res.status(201).json({
    success: true,
    message: 'تم إنشاء الفندق بنجاح',
    data: {
      hotel
    }
  });
});

// @desc    Update hotel (Admin only)
// @route   PUT /api/hotels/:id
// @access  Private/Admin
const updateHotel = asyncHandler(async (req, res) => {
  const hotel = await Hotel.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  );

  if (!hotel) {
    return res.status(404).json({
      success: false,
      message: 'الفندق غير موجود'
    });
  }

  res.status(200).json({
    success: true,
    message: 'تم تحديث الفندق بنجاح',
    data: {
      hotel
    }
  });
});

// @desc    Delete hotel (Admin only)
// @route   DELETE /api/hotels/:id
// @access  Private/Admin
const deleteHotel = asyncHandler(async (req, res) => {
  const hotel = await Hotel.findById(req.params.id);

  if (!hotel) {
    return res.status(404).json({
      success: false,
      message: 'الفندق غير موجود'
    });
  }

  // Soft delete
  hotel.isActive = false;
  await hotel.save();

  res.status(200).json({
    success: true,
    message: 'تم حذف الفندق بنجاح'
  });
});

// @desc    Get hotel statistics (Admin only)
// @route   GET /api/hotels/stats
// @access  Private/Admin
const getHotelStats = asyncHandler(async (req, res) => {
  const totalHotels = await Hotel.countDocuments({ isActive: true });
  const partnerHotels = await Hotel.countDocuments({ isPartner: true, isActive: true });

  const starRatingStats = await Hotel.aggregate([
    { $match: { isActive: true } },
    {
      $group: {
        _id: '$starRating',
        count: { $sum: 1 },
        avgRating: { $avg: '$rating.average' }
      }
    },
    { $sort: { _id: -1 } }
  ]);

  const destinationStats = await Hotel.aggregate([
    { $match: { isActive: true } },
    {
      $lookup: {
        from: 'destinations',
        localField: 'destination',
        foreignField: '_id',
        as: 'destinationInfo'
      }
    },
    { $unwind: '$destinationInfo' },
    {
      $group: {
        _id: '$destinationInfo.city',
        count: { $sum: 1 },
        avgStars: { $avg: '$starRating' }
      }
    },
    { $sort: { count: -1 } },
    { $limit: 10 }
  ]);

  res.status(200).json({
    success: true,
    data: {
      totalHotels,
      partnerHotels,
      starRatingBreakdown: starRatingStats,
      topDestinations: destinationStats
    }
  });
});

module.exports = {
  searchHotels,
  getHotelById,
  getHotelsByDestination,
  getPartnerHotels,
  createHotel,
  updateHotel,
  deleteHotel,
  getHotelStats
};
