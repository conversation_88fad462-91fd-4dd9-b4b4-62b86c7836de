const Booking = require('../models/Booking');
const Flight = require('../models/Flight');
const User = require('../models/User');
const LoyaltyAccount = require('../models/LoyaltyAccount');
const { asyncHandler } = require('../middlewares/errorHandler');

// @desc    Create new booking
// @route   POST /api/bookings
// @access  Private
const createBooking = asyncHandler(async (req, res) => {
  const {
    tripType,
    flights,
    contactInfo,
    addons = [],
    specialRequests = []
  } = req.body;

  // Validate flights and calculate pricing
  let totalPrice = 0;
  let processedFlights = [];

  for (let flightBooking of flights) {
    const flight = await Flight.findById(flightBooking.flight);
    
    if (!flight) {
      return res.status(404).json({
        success: false,
        message: `الرحلة غير موجودة: ${flightBooking.flight}`
      });
    }

    // Check availability
    const requestedClass = flightBooking.class;
    const passengerCount = flightBooking.passengers.length;
    
    if (flight.availability[requestedClass] < passengerCount) {
      return res.status(400).json({
        success: false,
        message: `لا توجد مقاعد كافية في درجة ${requestedClass} للرحلة ${flight.flightNumber}`
      });
    }

    // Calculate flight price
    const flightPrice = flight.pricing[requestedClass].totalPrice * passengerCount;
    totalPrice += flightPrice;

    processedFlights.push({
      ...flightBooking,
      flightDetails: flight,
      price: flightPrice
    });
  }

  // Calculate addons price
  const addonsPrice = addons.reduce((sum, addon) => sum + (addon.price || 0), 0);
  totalPrice += addonsPrice;

  // Apply loyalty discount if applicable
  let discount = 0;
  const user = await User.findById(req.user.id).populate('loyaltyAccount');
  
  if (user.loyaltyAccount) {
    const loyaltyAccount = user.loyaltyAccount;
    // Apply 2% discount for Silver and above
    if (['Silver', 'Gold', 'Platinum', 'Diamond'].includes(loyaltyAccount.tier)) {
      discount = totalPrice * 0.02;
    }
  }

  const finalPrice = totalPrice - discount;

  // Create booking
  const booking = await Booking.create({
    user: req.user.id,
    tripType,
    flights: flights.map(f => ({
      flight: f.flight,
      class: f.class,
      passengers: f.passengers,
      seats: f.seats || []
    })),
    contactInfo,
    pricing: {
      subtotal: totalPrice - addonsPrice,
      taxes: 0, // Will be calculated based on route
      fees: 0,
      addons: addonsPrice,
      discount,
      total: finalPrice
    },
    addons,
    specialRequests,
    payment: {
      method: req.body.paymentMethod || 'credit-card',
      status: 'pending',
      currency: 'USD'
    }
  });

  // Update flight availability
  for (let flightBooking of flights) {
    await Flight.findByIdAndUpdate(
      flightBooking.flight,
      {
        $inc: {
          [`availability.${flightBooking.class}`]: -flightBooking.passengers.length
        }
      }
    );
  }

  // Add booking to user
  await User.findByIdAndUpdate(
    req.user.id,
    { $push: { bookings: booking._id } }
  );

  // Award loyalty points
  if (user.loyaltyAccount) {
    const pointsEarned = Math.floor(finalPrice / 10); // 1 point per $10
    await LoyaltyAccount.findByIdAndUpdate(
      user.loyaltyAccount._id,
      {
        $inc: {
          'points.current': pointsEarned,
          'points.lifetime': pointsEarned,
          'points.thisYear': pointsEarned
        },
        $push: {
          transactions: {
            type: 'earned',
            points: pointsEarned,
            description: `نقاط من الحجز ${booking.bookingReference}`,
            booking: booking._id
          }
        }
      }
    );
  }

  // Populate booking details for response
  const populatedBooking = await Booking.findById(booking._id)
    .populate('flights.flight', 'flightNumber airline route duration')
    .populate('user', 'name email phone');

  res.status(201).json({
    success: true,
    message: 'تم إنشاء الحجز بنجاح',
    data: {
      booking: populatedBooking
    }
  });
});

// @desc    Get user bookings
// @route   GET /api/bookings
// @access  Private
const getUserBookings = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const filter = { user: req.user.id };
  
  if (req.query.status) {
    filter.status = req.query.status;
  }

  const bookings = await Booking.find(filter)
    .populate('flights.flight', 'flightNumber airline route duration status')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  const total = await Booking.countDocuments(filter);

  res.status(200).json({
    success: true,
    data: {
      bookings,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Get booking by ID
// @route   GET /api/bookings/:id
// @access  Private
const getBookingById = asyncHandler(async (req, res) => {
  const booking = await Booking.findById(req.params.id)
    .populate('flights.flight')
    .populate('user', 'name email phone');

  if (!booking) {
    return res.status(404).json({
      success: false,
      message: 'الحجز غير موجود'
    });
  }

  // Check if user owns the booking or is admin
  if (booking.user._id.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'غير مصرح لك بالوصول إلى هذا الحجز'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      booking
    }
  });
});

// @desc    Get booking by reference
// @route   GET /api/bookings/reference/:reference
// @access  Public
const getBookingByReference = asyncHandler(async (req, res) => {
  const { reference } = req.params;
  const { email } = req.query;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: 'البريد الإلكتروني مطلوب للبحث عن الحجز'
    });
  }

  const booking = await Booking.findOne({
    bookingReference: reference.toUpperCase(),
    'contactInfo.email': email.toLowerCase()
  })
  .populate('flights.flight', 'flightNumber airline route duration status');

  if (!booking) {
    return res.status(404).json({
      success: false,
      message: 'الحجز غير موجود أو البريد الإلكتروني غير صحيح'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      booking
    }
  });
});

// @desc    Update booking
// @route   PUT /api/bookings/:id
// @access  Private
const updateBooking = asyncHandler(async (req, res) => {
  const booking = await Booking.findById(req.params.id);

  if (!booking) {
    return res.status(404).json({
      success: false,
      message: 'الحجز غير موجود'
    });
  }

  // Check ownership
  if (booking.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'غير مصرح لك بتعديل هذا الحجز'
    });
  }

  // Only allow updates for pending bookings
  if (booking.status !== 'pending') {
    return res.status(400).json({
      success: false,
      message: 'لا يمكن تعديل الحجز بعد التأكيد'
    });
  }

  const allowedUpdates = ['contactInfo', 'specialRequests'];
  const updates = {};

  allowedUpdates.forEach(field => {
    if (req.body[field]) {
      updates[field] = req.body[field];
    }
  });

  const updatedBooking = await Booking.findByIdAndUpdate(
    req.params.id,
    updates,
    { new: true, runValidators: true }
  ).populate('flights.flight');

  res.status(200).json({
    success: true,
    message: 'تم تحديث الحجز بنجاح',
    data: {
      booking: updatedBooking
    }
  });
});

// @desc    Cancel booking
// @route   DELETE /api/bookings/:id
// @access  Private
const cancelBooking = asyncHandler(async (req, res) => {
  const booking = await Booking.findById(req.params.id);

  if (!booking) {
    return res.status(404).json({
      success: false,
      message: 'الحجز غير موجود'
    });
  }

  // Check ownership
  if (booking.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'غير مصرح لك بإلغاء هذا الحجز'
    });
  }

  // Check if booking can be cancelled
  if (['cancelled', 'completed'].includes(booking.status)) {
    return res.status(400).json({
      success: false,
      message: 'لا يمكن إلغاء هذا الحجز'
    });
  }

  // Calculate refund amount based on cancellation policy
  let refundAmount = 0;
  const now = new Date();
  const departureTime = new Date(booking.flights[0].flight.route?.departure?.scheduledTime);
  const hoursUntilDeparture = (departureTime - now) / (1000 * 60 * 60);

  if (hoursUntilDeparture > 24) {
    refundAmount = booking.pricing.total * 0.8; // 80% refund
  } else if (hoursUntilDeparture > 2) {
    refundAmount = booking.pricing.total * 0.5; // 50% refund
  }
  // No refund if less than 2 hours

  // Update booking status
  booking.status = 'cancelled';
  booking.cancellation = {
    reason: req.body.reason || 'إلغاء بواسطة المستخدم',
    cancelledAt: new Date(),
    refundAmount,
    refundStatus: refundAmount > 0 ? 'pending' : 'not-applicable'
  };

  await booking.save();

  // Restore flight availability
  for (let flightBooking of booking.flights) {
    await Flight.findByIdAndUpdate(
      flightBooking.flight,
      {
        $inc: {
          [`availability.${flightBooking.class}`]: flightBooking.passengers.length
        }
      }
    );
  }

  res.status(200).json({
    success: true,
    message: 'تم إلغاء الحجز بنجاح',
    data: {
      booking,
      refundAmount
    }
  });
});

// @desc    Confirm payment
// @route   POST /api/bookings/:id/confirm-payment
// @access  Private
const confirmPayment = asyncHandler(async (req, res) => {
  const { transactionId, paymentMethod } = req.body;

  const booking = await Booking.findById(req.params.id);

  if (!booking) {
    return res.status(404).json({
      success: false,
      message: 'الحجز غير موجود'
    });
  }

  // Check ownership
  if (booking.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'غير مصرح لك بالوصول إلى هذا الحجز'
    });
  }

  if (booking.payment.status === 'completed') {
    return res.status(400).json({
      success: false,
      message: 'تم تأكيد الدفع مسبقاً'
    });
  }

  // Update payment status
  booking.payment.status = 'completed';
  booking.payment.transactionId = transactionId;
  booking.payment.method = paymentMethod;
  booking.payment.paidAt = new Date();
  booking.status = 'confirmed';

  await booking.save();

  res.status(200).json({
    success: true,
    message: 'تم تأكيد الدفع والحجز بنجاح',
    data: {
      booking
    }
  });
});

module.exports = {
  createBooking,
  getUserBookings,
  getBookingById,
  getBookingByReference,
  updateBooking,
  cancelBooking,
  confirmPayment
};
