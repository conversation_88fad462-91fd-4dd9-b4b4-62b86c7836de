import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  Plane, 
  Menu, 
  X, 
  Globe, 
  User, 
  ChevronDown,
  Search,
  Bell
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const Header: React.FC = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

  const isRTL = i18n.language === 'ar';

  const toggleLanguage = () => {
    const newLang = i18n.language === 'ar' ? 'en' : 'ar';
    i18n.changeLanguage(newLang);
    document.dir = newLang === 'ar' ? 'rtl' : 'ltr';
  };

  const navigationItems = [
    {
      key: 'planAndBook',
      label: t('navigation.planAndBook'),
      href: '/plan-and-book',
      submenu: [
        { label: t('planAndBook.planJourney.title'), href: '/plan-journey' },
        { label: t('planAndBook.book.title'), href: '/book' },
        { label: t('planAndBook.beforeFly.title'), href: '/before-fly' }
      ]
    },
    {
      key: 'prepareTrip',
      label: t('navigation.prepareTrip'),
      href: '/prepare-trip',
      submenu: [
        { label: t('prepareTrip.baggageInfo.title'), href: '/baggage-info' },
        { label: t('prepareTrip.manageBooking.title'), href: '/manage-booking' },
        { label: t('prepareTrip.enhanceTrip.title'), href: '/enhance-trip' }
      ]
    },
    {
      key: 'information',
      label: t('navigation.information'),
      href: '/information',
      submenu: [
        { label: t('information.aboutCompany.title'), href: '/about' },
        { label: t('information.help.title'), href: '/help' },
        { label: t('information.specialServices.title'), href: '/special-services' }
      ]
    },
    {
      key: 'nasmiles',
      label: t('navigation.nasmiles'),
      href: '/nasmiles',
      submenu: [
        { label: t('nasmiles.earnPoints'), href: '/nasmiles/earn' },
        { label: t('nasmiles.redeemRewards'), href: '/nasmiles/redeem' },
        { label: t('nasmiles.tierBenefits'), href: '/nasmiles/benefits' },
        { label: t('nasmiles.myAccount'), href: '/nasmiles/account' }
      ]
    }
  ];

  const isActiveRoute = (href: string) => {
    return location.pathname.startsWith(href);
  };

  return (
    <header className="bg-white shadow-soft sticky top-0 z-50 backdrop-blur-glass">
      <div className="container-custom">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="flex items-center justify-center w-10 h-10 lg:w-12 lg:h-12 gradient-primary rounded-xl">
              <Plane className="w-6 h-6 lg:w-7 lg:h-7 text-white" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl lg:text-2xl font-bold text-secondary-900">
                {isRTL ? 'طرق السماء' : 'FlyWay'}
              </h1>
              <p className="text-xs lg:text-sm text-secondary-500">
                {isRTL ? 'رحلات استثنائية' : 'Exceptional Journeys'}
              </p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            {navigationItems.map((item) => (
              <div key={item.key} className="relative group">
                <Link
                  to={item.href}
                  className={`flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                    isActiveRoute(item.href)
                      ? 'text-primary-600 bg-primary-50'
                      : 'text-secondary-700 hover:text-primary-600 hover:bg-secondary-50'
                  }`}
                >
                  <span>{item.label}</span>
                  <ChevronDown className="w-4 h-4 transition-transform group-hover:rotate-180" />
                </Link>
                
                {/* Dropdown Menu */}
                <div className="absolute top-full left-0 rtl:left-auto rtl:right-0 mt-2 w-64 bg-white rounded-xl shadow-large border border-secondary-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div className="p-2">
                    {item.submenu.map((subItem) => (
                      <Link
                        key={subItem.href}
                        to={subItem.href}
                        className="block px-4 py-3 text-sm text-secondary-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                      >
                        {subItem.label}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {/* Search Button */}
            <button className="p-2 text-secondary-600 hover:text-primary-600 hover:bg-secondary-50 rounded-lg transition-colors duration-200">
              <Search className="w-5 h-5" />
            </button>

            {/* Notifications */}
            <button className="p-2 text-secondary-600 hover:text-primary-600 hover:bg-secondary-50 rounded-lg transition-colors duration-200 relative">
              <Bell className="w-5 h-5" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-error-500 rounded-full"></span>
            </button>

            {/* Language Switcher */}
            <div className="relative">
              <button
                onClick={() => setIsLanguageMenuOpen(!isLanguageMenuOpen)}
                className="flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 text-sm font-medium text-secondary-700 hover:text-primary-600 hover:bg-secondary-50 rounded-lg transition-colors duration-200"
              >
                <Globe className="w-4 h-4" />
                <span className="hidden sm:block">{isRTL ? 'العربية' : 'English'}</span>
                <ChevronDown className="w-4 h-4" />
              </button>
              
              <AnimatePresence>
                {isLanguageMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 rtl:right-auto rtl:left-0 mt-2 w-40 bg-white rounded-xl shadow-large border border-secondary-100 z-50"
                  >
                    <div className="p-2">
                      <button
                        onClick={() => {
                          toggleLanguage();
                          setIsLanguageMenuOpen(false);
                        }}
                        className="w-full text-left px-4 py-3 text-sm text-secondary-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                      >
                        {isRTL ? 'English' : 'العربية'}
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className="flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 text-sm font-medium text-secondary-700 hover:text-primary-600 hover:bg-secondary-50 rounded-lg transition-colors duration-200"
              >
                <User className="w-4 h-4" />
                <span className="hidden sm:block">{t('navigation.login')}</span>
                <ChevronDown className="w-4 h-4" />
              </button>
              
              <AnimatePresence>
                {isUserMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 rtl:right-auto rtl:left-0 mt-2 w-48 bg-white rounded-xl shadow-large border border-secondary-100 z-50"
                  >
                    <div className="p-2">
                      <Link
                        to="/login"
                        className="block px-4 py-3 text-sm text-secondary-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        {t('navigation.login')}
                      </Link>
                      <Link
                        to="/register"
                        className="block px-4 py-3 text-sm text-secondary-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        {t('navigation.register')}
                      </Link>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2 text-secondary-600 hover:text-primary-600 hover:bg-secondary-50 rounded-lg transition-colors duration-200"
            >
              {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden border-t border-secondary-100 bg-white"
            >
              <div className="py-4 space-y-2">
                {navigationItems.map((item) => (
                  <div key={item.key}>
                    <Link
                      to={item.href}
                      className={`block px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                        isActiveRoute(item.href)
                          ? 'text-primary-600 bg-primary-50'
                          : 'text-secondary-700 hover:text-primary-600 hover:bg-secondary-50'
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {item.label}
                    </Link>
                    <div className="ml-4 rtl:ml-0 rtl:mr-4 space-y-1">
                      {item.submenu.map((subItem) => (
                        <Link
                          key={subItem.href}
                          to={subItem.href}
                          className="block px-4 py-2 text-sm text-secondary-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {subItem.label}
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </header>
  );
};

export default Header;
