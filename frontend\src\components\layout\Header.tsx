import React from 'react';
import { Link } from 'react-router-dom';

const Header: React.FC = () => {
  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-8 h-8 bg-blue-600 rounded-lg"></div>
            <span className="text-xl font-bold text-gray-800">طرق السماء</span>
            <span className="text-sm text-gray-500">FlyWay</span>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
            <Link to="/" className="text-gray-700 hover:text-blue-600 transition-colors">
              الرئيسية
            </Link>
            <Link to="/plan-book" className="text-gray-700 hover:text-blue-600 transition-colors">
              خطط واحجز
            </Link>
            <Link to="/prepare" className="text-gray-700 hover:text-blue-600 transition-colors">
              استعد للرحلة
            </Link>
            <Link to="/info" className="text-gray-700 hover:text-blue-600 transition-colors">
              معلومات
            </Link>
            <Link to="/nasmiles" className="text-gray-700 hover:text-blue-600 transition-colors">
              nasmiles
            </Link>
          </nav>

          {/* Language Toggle */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <button className="text-sm text-gray-600 hover:text-blue-600 transition-colors">
              EN | العربية
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
