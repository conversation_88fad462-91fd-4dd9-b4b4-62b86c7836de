import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';

const Header: React.FC = () => {
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const servicesRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (servicesRef.current && !servicesRef.current.contains(event.target as Node)) {
        setIsServicesOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 rtl:space-x-reverse hover:opacity-80 transition-opacity">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-lg">✈</span>
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-gray-800">طرق السماء</span>
              <span className="text-xs text-gray-500 -mt-1">FlyWay</span>
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            <Link to="/" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
              الرئيسية
            </Link>

            {/* Services Dropdown */}
            <div className="relative" ref={servicesRef}>
              <button
                onClick={() => setIsServicesOpen(!isServicesOpen)}
                className="flex items-center space-x-1 rtl:space-x-reverse text-gray-700 hover:text-blue-600 transition-colors font-medium"
              >
                <span>خدماتنا</span>
                <svg className={`w-4 h-4 transition-transform ${isServicesOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {isServicesOpen && (
                <div className="absolute top-full right-0 rtl:right-auto rtl:left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-100 py-2 animate-fade-in-down">
                  <Link to="/plan-book" className="block px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                    <div className="font-medium">خطط واحجز</div>
                    <div className="text-sm text-gray-500">خطط لرحلتك واحجز بأفضل الأسعار</div>
                  </Link>
                  <Link to="/prepare" className="block px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                    <div className="font-medium">استعد للرحلة</div>
                    <div className="text-sm text-gray-500">كل ما تحتاجه قبل السفر</div>
                  </Link>
                  <Link to="/info" className="block px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                    <div className="font-medium">معلومات</div>
                    <div className="text-sm text-gray-500">معلومات مهمة ومساعدة</div>
                  </Link>
                  <Link to="/nasmiles" className="block px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                    <div className="font-medium">nasmiles</div>
                    <div className="text-sm text-gray-500">برنامج الولاء والمكافآت</div>
                  </Link>
                </div>
              )}
            </div>

            <Link to="/about" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
              من نحن
            </Link>
            <Link to="/contact" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
              اتصل بنا
            </Link>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {/* Language Toggle */}
            <button className="hidden md:flex items-center space-x-1 rtl:space-x-reverse text-sm text-gray-600 hover:text-blue-600 transition-colors">
              <span>🌐</span>
              <span>العربية</span>
            </button>

            {/* Login Button */}
            <button className="hidden md:block bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
              تسجيل الدخول
            </button>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2 text-gray-600 hover:text-blue-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-100 bg-white animate-fade-in-down">
            <div className="px-4 py-4 space-y-4">
              <Link
                to="/"
                className="block text-gray-700 hover:text-blue-600 transition-colors font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                الرئيسية
              </Link>

              <div className="space-y-2">
                <div className="text-gray-500 text-sm font-medium">خدماتنا</div>
                <Link
                  to="/plan-book"
                  className="block pr-4 text-gray-600 hover:text-blue-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  خطط واحجز
                </Link>
                <Link
                  to="/prepare"
                  className="block pr-4 text-gray-600 hover:text-blue-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  استعد للرحلة
                </Link>
                <Link
                  to="/info"
                  className="block pr-4 text-gray-600 hover:text-blue-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  معلومات
                </Link>
                <Link
                  to="/nasmiles"
                  className="block pr-4 text-gray-600 hover:text-blue-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  nasmiles
                </Link>
              </div>

              <Link
                to="/about"
                className="block text-gray-700 hover:text-blue-600 transition-colors font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                من نحن
              </Link>

              <Link
                to="/contact"
                className="block text-gray-700 hover:text-blue-600 transition-colors font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                اتصل بنا
              </Link>

              <div className="pt-4 border-t border-gray-100 space-y-3">
                <button className="flex items-center space-x-2 rtl:space-x-reverse text-gray-600">
                  <span>🌐</span>
                  <span className="text-sm">العربية</span>
                </button>
                <button className="w-full bg-blue-600 text-white py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                  تسجيل الدخول
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
