import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Star, Quote } from 'lucide-react';

const TestimonialsSection: React.FC = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const testimonials = [
    {
      id: 1,
      name: isRTL ? 'أحمد محمد' : '<PERSON>',
      title: isRTL ? 'رجل أعمال' : 'Business Executive',
      location: isRTL ? 'الرياض، السعودية' : 'Riyadh, Saudi Arabia',
      rating: 5,
      comment: isRTL 
        ? 'تجربة رائعة مع طرق السماء! الخدمة ممتازة والأسعار تنافسية. أنصح بها بشدة لجميع المسافرين.'
        : 'Amazing experience with FlyWay! Excellent service and competitive prices. Highly recommend it to all travelers.',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      date: isRTL ? 'منذ أسبوعين' : '2 weeks ago'
    },
    {
      id: 2,
      name: isRTL ? 'فاطمة العلي' : 'Fatima Al-Ali',
      title: isRTL ? 'مهندسة' : 'Engineer',
      location: isRTL ? 'دبي، الإمارات' : 'Dubai, UAE',
      rating: 5,
      comment: isRTL 
        ? 'حجزت رحلة عائلية وكانت التجربة مذهلة من البداية للنهاية. فريق خدمة العملاء محترف جداً.'
        : 'Booked a family trip and the experience was amazing from start to finish. Customer service team is very professional.',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      date: isRTL ? 'منذ شهر' : '1 month ago'
    },
    {
      id: 3,
      name: isRTL ? 'خالد السعيد' : 'Khalid Al-Saeed',
      title: isRTL ? 'طبيب' : 'Doctor',
      location: isRTL ? 'جدة، السعودية' : 'Jeddah, Saudi Arabia',
      rating: 5,
      comment: isRTL 
        ? 'أفضل شركة طيران تعاملت معها. الطائرات حديثة والخدمة على متن الطائرة ممتازة.'
        : 'Best airline I have dealt with. Modern aircraft and excellent in-flight service.',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      date: isRTL ? 'منذ 3 أسابيع' : '3 weeks ago'
    },
    {
      id: 4,
      name: isRTL ? 'مريم أحمد' : 'Mariam Ahmed',
      title: isRTL ? 'مصممة' : 'Designer',
      location: isRTL ? 'الكويت' : 'Kuwait',
      rating: 5,
      comment: isRTL 
        ? 'موقع سهل الاستخدام وحجز سريع. حصلت على أفضل سعر لرحلتي إلى لندن.'
        : 'Easy-to-use website and quick booking. Got the best price for my trip to London.',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      date: isRTL ? 'منذ أسبوع' : '1 week ago'
    },
    {
      id: 5,
      name: isRTL ? 'عبدالله الحربي' : 'Abdullah Al-Harbi',
      title: isRTL ? 'مدير مبيعات' : 'Sales Manager',
      location: isRTL ? 'الدمام، السعودية' : 'Dammam, Saudi Arabia',
      rating: 5,
      comment: isRTL 
        ? 'برنامج الولاء ممتاز! أكسب نقاط مع كل رحلة وأستفيد من العروض الحصرية.'
        : 'Excellent loyalty program! I earn points with every flight and benefit from exclusive offers.',
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      date: isRTL ? 'منذ 5 أيام' : '5 days ago'
    },
    {
      id: 6,
      name: isRTL ? 'نورا المطيري' : 'Nora Al-Mutairi',
      title: isRTL ? 'معلمة' : 'Teacher',
      location: isRTL ? 'الرياض، السعودية' : 'Riyadh, Saudi Arabia',
      rating: 5,
      comment: isRTL 
        ? 'خدمة عملاء رائعة! ساعدوني في تغيير موعد رحلتي بدون أي رسوم إضافية.'
        : 'Great customer service! They helped me change my flight date without any additional fees.',
      image: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
      date: isRTL ? 'منذ 10 أيام' : '10 days ago'
    }
  ];

  return (
    <section className="section-padding bg-gradient-to-br from-secondary-50 to-primary-50">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="heading-2 mb-6">
            {isRTL ? 'ماذا يقول عملاؤنا؟' : 'What Our Customers Say?'}
          </h2>
          <p className="text-body-lg max-w-3xl mx-auto">
            {isRTL 
              ? 'آراء وتجارب حقيقية من عملائنا الكرام الذين اختاروا طرق السماء لرحلاتهم'
              : 'Real opinions and experiences from our valued customers who chose FlyWay for their journeys'
            }
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="card-hover h-full p-6 relative overflow-hidden">
                {/* Quote Icon */}
                <div className="absolute top-4 right-4 rtl:right-auto rtl:left-4 opacity-10">
                  <Quote className="w-12 h-12 text-primary-600" />
                </div>

                {/* Rating */}
                <div className="flex items-center space-x-1 rtl:space-x-reverse mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>

                {/* Comment */}
                <p className="text-secondary-700 leading-relaxed mb-6 relative z-10">
                  "{testimonial.comment}"
                </p>

                {/* Customer Info */}
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className="relative">
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover ring-2 ring-primary-100"
                    />
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-secondary-900 group-hover:text-primary-600 transition-colors duration-300">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-secondary-600">
                      {testimonial.title}
                    </p>
                    <p className="text-xs text-secondary-500">
                      {testimonial.location} • {testimonial.date}
                    </p>
                  </div>
                </div>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-accent-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Trust Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-2xl p-8 shadow-soft">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-primary-600 mb-2">4.9/5</div>
                <div className="text-secondary-600 mb-2">{isRTL ? 'تقييم العملاء' : 'Customer Rating'}</div>
                <div className="flex justify-center space-x-1 rtl:space-x-reverse">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                  ))}
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">98%</div>
                <div className="text-secondary-600">{isRTL ? 'معدل التوصية' : 'Recommendation Rate'}</div>
                <div className="text-sm text-secondary-500 mt-1">
                  {isRTL ? 'من العملاء ينصحون بنا' : 'of customers recommend us'}
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-4xl font-bold text-accent-600 mb-2">24/7</div>
                <div className="text-secondary-600">{isRTL ? 'دعم العملاء' : 'Customer Support'}</div>
                <div className="text-sm text-secondary-500 mt-1">
                  {isRTL ? 'متاح على مدار الساعة' : 'Available around the clock'}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
