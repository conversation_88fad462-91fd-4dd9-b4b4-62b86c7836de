import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  Plane, 
  Calendar, 
  Info, 
  Star,
  ArrowRight,
  ArrowLeft,
  MapPin,
  Luggage,
  CreditCard,
  Shield
} from 'lucide-react';

const ServicesSection: React.FC = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const mainServices = [
    {
      key: 'planAndBook',
      title: t('planAndBook.title'),
      description: isRTL 
        ? 'خطط واحجز رحلتك بسهولة مع أفضل الأسعار والعروض الحصرية'
        : 'Plan and book your trip easily with the best prices and exclusive offers',
      icon: Plane,
      color: 'from-blue-500 to-blue-600',
      image: 'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      href: '/plan-and-book',
      features: [
        t('planAndBook.planJourney.title'),
        t('planAndBook.book.title'),
        t('planAndBook.beforeFly.title')
      ]
    },
    {
      key: 'prepareTrip',
      title: t('prepareTrip.title'),
      description: isRTL 
        ? 'استعد لرحلتك مع جميع المعلومات والخدمات التي تحتاجها'
        : 'Prepare for your trip with all the information and services you need',
      icon: Calendar,
      color: 'from-green-500 to-green-600',
      image: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      href: '/prepare-trip',
      features: [
        t('prepareTrip.baggageInfo.title'),
        t('prepareTrip.manageBooking.title'),
        t('prepareTrip.enhanceTrip.title')
      ]
    },
    {
      key: 'information',
      title: t('information.title'),
      description: isRTL 
        ? 'احصل على جميع المعلومات والمساعدة التي تحتاجها'
        : 'Get all the information and help you need',
      icon: Info,
      color: 'from-purple-500 to-purple-600',
      image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      href: '/information',
      features: [
        t('information.aboutCompany.title'),
        t('information.help.title'),
        t('information.specialServices.title')
      ]
    },
    {
      key: 'nasmiles',
      title: t('nasmiles.title'),
      description: isRTL 
        ? 'اكسب النقاط واستمتع بمزايا حصرية مع برنامج الولاء الخاص بنا'
        : 'Earn points and enjoy exclusive benefits with our loyalty program',
      icon: Star,
      color: 'from-yellow-500 to-orange-500',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      href: '/nasmiles',
      features: [
        t('nasmiles.earnPoints'),
        t('nasmiles.redeemRewards'),
        t('nasmiles.tierBenefits')
      ]
    }
  ];

  const additionalFeatures = [
    {
      icon: MapPin,
      title: isRTL ? '85+ وجهة' : '85+ Destinations',
      description: isRTL ? 'وجهات حول العالم' : 'Destinations worldwide'
    },
    {
      icon: Shield,
      title: isRTL ? 'حجز آمن' : 'Secure Booking',
      description: isRTL ? 'حماية كاملة لبياناتك' : 'Complete protection for your data'
    },
    {
      icon: CreditCard,
      title: isRTL ? 'دفع مرن' : 'Flexible Payment',
      description: isRTL ? 'طرق دفع متعددة' : 'Multiple payment methods'
    },
    {
      icon: Luggage,
      title: isRTL ? 'خدمة شاملة' : 'Complete Service',
      description: isRTL ? 'من الحجز إلى الوصول' : 'From booking to arrival'
    }
  ];

  return (
    <section className="section-padding bg-gradient-to-br from-secondary-50 to-white">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="heading-2 mb-6">
            {isRTL ? 'خدماتنا الرئيسية' : 'Our Main Services'}
          </h2>
          <p className="text-body-lg max-w-3xl mx-auto">
            {isRTL 
              ? 'نقدم مجموعة شاملة من الخدمات لجعل تجربة سفرك استثنائية ومريحة من البداية إلى النهاية'
              : 'We offer a comprehensive range of services to make your travel experience exceptional and comfortable from start to finish'
            }
          </p>
        </motion.div>

        {/* Main Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
          {mainServices.map((service, index) => (
            <motion.div
              key={service.key}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <Link to={service.href} className="block">
                <div className="card-hover h-full overflow-hidden">
                  {/* Service Image */}
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={service.image}
                      alt={service.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-80`}></div>
                    <div className="absolute inset-0 bg-black/20"></div>
                    
                    {/* Service Icon */}
                    <div className="absolute top-6 left-6 rtl:left-auto rtl:right-6">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                        <service.icon className="w-6 h-6 text-white" />
                      </div>
                    </div>

                    {/* Arrow Icon */}
                    <div className="absolute top-6 right-6 rtl:right-auto rtl:left-6">
                      <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-white/30 transition-colors duration-300">
                        {isRTL ? 
                          <ArrowLeft className="w-5 h-5 text-white" /> : 
                          <ArrowRight className="w-5 h-5 text-white" />
                        }
                      </div>
                    </div>
                  </div>

                  {/* Service Content */}
                  <div className="p-6">
                    <h3 className="heading-4 mb-3 group-hover:text-primary-600 transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-body mb-4">
                      {service.description}
                    </p>

                    {/* Service Features */}
                    <div className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-secondary-600">
                          <div className="w-1.5 h-1.5 bg-primary-500 rounded-full"></div>
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Additional Features */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {additionalFeatures.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center p-6 bg-white rounded-2xl shadow-soft hover:shadow-medium transition-shadow duration-300"
            >
              <div className="w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <feature.icon className="w-8 h-8 text-primary-600" />
              </div>
              <h4 className="text-lg font-semibold text-secondary-900 mb-2">
                {feature.title}
              </h4>
              <p className="text-secondary-600 text-sm">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary-600 to-accent-500 rounded-3xl p-8 lg:p-12 text-white">
            <h3 className="heading-3 text-white mb-4">
              {isRTL ? 'جاهز لبدء رحلتك؟' : 'Ready to Start Your Journey?'}
            </h3>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              {isRTL 
                ? 'انضم إلى ملايين المسافرين الذين يثقون في طرق السماء لرحلاتهم الاستثنائية'
                : 'Join millions of travelers who trust FlyWay for their exceptional journeys'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/flights" className="btn-accent btn-lg bg-white text-primary-600 hover:bg-secondary-50">
                {isRTL ? 'ابحث عن رحلات' : 'Search Flights'}
              </Link>
              <Link to="/nasmiles" className="btn-outline btn-lg border-white text-white hover:bg-white hover:text-primary-600">
                {isRTL ? 'انضم لبرنامج الولاء' : 'Join Loyalty Program'}
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesSection;
