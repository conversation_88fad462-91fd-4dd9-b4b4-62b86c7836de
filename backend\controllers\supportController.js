const { FAQ, Contact, SupportTicket, SpecialService } = require('../models/Support');
const { asyncHandler } = require('../middlewares/errorHandler');

// @desc    Get FAQs by category
// @route   GET /api/support/faq
// @access  Public
const getFAQs = asyncHandler(async (req, res) => {
  const { category, search } = req.query;
  const limit = parseInt(req.query.limit) || 20;

  let query = { isActive: true };

  if (category) {
    query.category = category;
  }

  if (search) {
    query.$or = [
      { 'question.ar': new RegExp(search, 'i') },
      { 'question.en': new RegExp(search, 'i') },
      { 'answer.ar': new RegExp(search, 'i') },
      { 'answer.en': new RegExp(search, 'i') },
      { tags: new RegExp(search, 'i') }
    ];
  }

  const faqs = await FAQ.find(query)
    .sort({ order: 1, createdAt: -1 })
    .limit(limit);

  // Group by category
  const faqsByCategory = faqs.reduce((acc, faq) => {
    if (!acc[faq.category]) {
      acc[faq.category] = [];
    }
    acc[faq.category].push(faq);
    return acc;
  }, {});

  res.status(200).json({
    success: true,
    data: {
      faqs: faqsByCategory,
      total: faqs.length
    }
  });
});

// @desc    Get FAQ categories
// @route   GET /api/support/faq/categories
// @access  Public
const getFAQCategories = asyncHandler(async (req, res) => {
  const categories = [
    {
      code: 'booking',
      name: { ar: 'الحجز', en: 'Booking' },
      description: { ar: 'أسئلة حول عملية الحجز', en: 'Questions about booking process' },
      icon: 'calendar'
    },
    {
      code: 'payment',
      name: { ar: 'الدفع', en: 'Payment' },
      description: { ar: 'أسئلة حول طرق الدفع والفواتير', en: 'Questions about payment methods and billing' },
      icon: 'credit-card'
    },
    {
      code: 'baggage',
      name: { ar: 'الأمتعة', en: 'Baggage' },
      description: { ar: 'أسئلة حول الأمتعة والوزن المسموح', en: 'Questions about baggage and weight allowance' },
      icon: 'luggage'
    },
    {
      code: 'check-in',
      name: { ar: 'تسجيل الدخول', en: 'Check-in' },
      description: { ar: 'أسئلة حول تسجيل الدخول والمقاعد', en: 'Questions about check-in and seat selection' },
      icon: 'check'
    },
    {
      code: 'loyalty',
      name: { ar: 'برنامج الولاء', en: 'Loyalty Program' },
      description: { ar: 'أسئلة حول برنامج nasmiles', en: 'Questions about nasmiles program' },
      icon: 'star'
    },
    {
      code: 'refunds',
      name: { ar: 'الاسترداد', en: 'Refunds' },
      description: { ar: 'أسئلة حول الإلغاء والاسترداد', en: 'Questions about cancellation and refunds' },
      icon: 'refresh'
    }
  ];

  res.status(200).json({
    success: true,
    data: {
      categories
    }
  });
});

// @desc    Get contact information
// @route   GET /api/support/contact
// @access  Public
const getContactInfo = asyncHandler(async (req, res) => {
  const { type, country } = req.query;

  let query = { isActive: true };

  if (type) {
    query.type = type;
  }

  if (country) {
    query['location.country'] = new RegExp(country, 'i');
  }

  const contacts = await Contact.find(query).sort({ type: 1, 'location.country': 1 });

  res.status(200).json({
    success: true,
    data: {
      contacts
    }
  });
});

// @desc    Create support ticket
// @route   POST /api/support/tickets
// @access  Public
const createSupportTicket = asyncHandler(async (req, res) => {
  const {
    category,
    subject,
    description,
    bookingReference,
    guestInfo
  } = req.body;

  const ticketData = {
    category,
    subject,
    description,
    bookingReference
  };

  if (req.user) {
    ticketData.user = req.user.id;
  } else {
    ticketData.guestInfo = guestInfo;
  }

  const ticket = await SupportTicket.create(ticketData);

  res.status(201).json({
    success: true,
    message: 'تم إنشاء تذكرة الدعم بنجاح',
    data: {
      ticket: {
        ticketNumber: ticket.ticketNumber,
        category: ticket.category,
        subject: ticket.subject,
        status: ticket.status,
        createdAt: ticket.createdAt
      }
    }
  });
});

// @desc    Get user support tickets
// @route   GET /api/support/tickets
// @access  Private
const getUserSupportTickets = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const tickets = await SupportTicket.find({
    user: req.user.id,
    isActive: true
  })
  .sort({ createdAt: -1 })
  .skip(skip)
  .limit(limit)
  .select('ticketNumber category subject status createdAt');

  const total = await SupportTicket.countDocuments({
    user: req.user.id,
    isActive: true
  });

  res.status(200).json({
    success: true,
    data: {
      tickets,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Get support ticket by number
// @route   GET /api/support/tickets/:ticketNumber
// @access  Public
const getSupportTicketByNumber = asyncHandler(async (req, res) => {
  const { ticketNumber } = req.params;

  const ticket = await SupportTicket.findOne({
    ticketNumber: ticketNumber.toUpperCase(),
    isActive: true
  }).populate('user', 'name email');

  if (!ticket) {
    return res.status(404).json({
      success: false,
      message: 'تذكرة الدعم غير موجودة'
    });
  }

  // Check if user owns the ticket or is admin
  if (req.user && ticket.user && ticket.user._id.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'غير مصرح لك بعرض هذه التذكرة'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      ticket
    }
  });
});

// @desc    Get special services
// @route   GET /api/support/special-services
// @access  Public
const getSpecialServices = asyncHandler(async (req, res) => {
  const { category } = req.query;

  let query = { isActive: true };

  if (category) {
    query.category = category;
  }

  const services = await SpecialService.find(query).sort({ order: 1, category: 1 });

  // Group by category
  const servicesByCategory = services.reduce((acc, service) => {
    if (!acc[service.category]) {
      acc[service.category] = [];
    }
    acc[service.category].push(service);
    return acc;
  }, {});

  res.status(200).json({
    success: true,
    data: {
      services: servicesByCategory
    }
  });
});

// @desc    Get special service categories
// @route   GET /api/support/special-services/categories
// @access  Public
const getSpecialServiceCategories = asyncHandler(async (req, res) => {
  const categories = [
    {
      code: 'elderly',
      name: { ar: 'كبار السن', en: 'Elderly Passengers' },
      description: { ar: 'خدمات خاصة لكبار السن', en: 'Special services for elderly passengers' },
      icon: 'user-plus'
    },
    {
      code: 'children',
      name: { ar: 'الأطفال', en: 'Children' },
      description: { ar: 'خدمات الأطفال والقُصر', en: 'Children and unaccompanied minor services' },
      icon: 'baby'
    },
    {
      code: 'pregnant',
      name: { ar: 'الحوامل', en: 'Pregnant Passengers' },
      description: { ar: 'خدمات خاصة للحوامل', en: 'Special services for pregnant passengers' },
      icon: 'heart'
    },
    {
      code: 'disabled',
      name: { ar: 'ذوي الاحتياجات الخاصة', en: 'Passengers with Disabilities' },
      description: { ar: 'خدمات ذوي الاحتياجات الخاصة', en: 'Services for passengers with disabilities' },
      icon: 'wheelchair'
    },
    {
      code: 'medical',
      name: { ar: 'الحالات الطبية', en: 'Medical Conditions' },
      description: { ar: 'خدمات للحالات الطبية الخاصة', en: 'Services for special medical conditions' },
      icon: 'medical'
    },
    {
      code: 'dietary',
      name: { ar: 'الوجبات الخاصة', en: 'Special Meals' },
      description: { ar: 'وجبات خاصة وحمية غذائية', en: 'Special meals and dietary requirements' },
      icon: 'utensils'
    }
  ];

  res.status(200).json({
    success: true,
    data: {
      categories
    }
  });
});

// @desc    Mark FAQ as helpful
// @route   POST /api/support/faq/:id/helpful
// @access  Public
const markFAQHelpful = asyncHandler(async (req, res) => {
  const { helpful } = req.body; // true or false

  const faq = await FAQ.findById(req.params.id);

  if (!faq) {
    return res.status(404).json({
      success: false,
      message: 'السؤال غير موجود'
    });
  }

  if (helpful) {
    faq.helpful.yes += 1;
  } else {
    faq.helpful.no += 1;
  }

  faq.viewCount += 1;
  await faq.save();

  res.status(200).json({
    success: true,
    message: 'شكراً لتقييمك'
  });
});

module.exports = {
  getFAQs,
  getFAQCategories,
  getContactInfo,
  createSupportTicket,
  getUserSupportTickets,
  getSupportTicketByNumber,
  getSpecialServices,
  getSpecialServiceCategories,
  markFAQHelpful
};
