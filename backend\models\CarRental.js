const mongoose = require('mongoose');

const carRentalSchema = new mongoose.Schema({
  company: {
    name: {
      type: String,
      required: [true, 'اسم شركة تأجير السيارات مطلوب']
    },
    code: {
      type: String,
      required: [true, 'رمز الشركة مطلوب'],
      uppercase: true
    },
    logo: String,
    rating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    }
  },
  destination: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Destination',
    required: true
  },
  location: {
    airport: {
      available: { type: Boolean, default: true },
      pickupFee: { type: Number, default: 0 }
    },
    cityCenter: {
      available: { type: Boolean, default: true },
      address: String,
      coordinates: {
        latitude: Number,
        longitude: Number
      }
    },
    hotel: {
      available: { type: Boolean, default: false },
      deliveryFee: { type: Number, default: 25 }
    }
  },
  vehicles: [{
    category: {
      type: String,
      enum: ['economy', 'compact', 'midsize', 'fullsize', 'luxury', 'suv', 'van'],
      required: true
    },
    name: {
      type: String,
      required: true
    },
    model: String,
    year: Number,
    transmission: {
      type: String,
      enum: ['manual', 'automatic'],
      default: 'automatic'
    },
    fuelType: {
      type: String,
      enum: ['gasoline', 'diesel', 'hybrid', 'electric'],
      default: 'gasoline'
    },
    capacity: {
      passengers: { type: Number, required: true },
      luggage: { type: Number, required: true },
      doors: { type: Number, default: 4 }
    },
    features: [{
      type: String,
      enum: ['air-conditioning', 'gps', 'bluetooth', 'usb-charging', 'wifi', 'child-seat', 'snow-chains']
    }],
    pricing: {
      dailyRate: { type: Number, required: true },
      weeklyRate: Number,
      monthlyRate: Number,
      currency: { type: String, default: 'USD' },
      insurance: {
        basic: { type: Number, default: 15 },
        comprehensive: { type: Number, default: 25 }
      },
      extras: {
        additionalDriver: { type: Number, default: 10 },
        gps: { type: Number, default: 8 },
        childSeat: { type: Number, default: 12 }
      }
    },
    images: [String],
    availability: {
      type: Number,
      default: 0
    }
  }],
  policies: {
    minimumAge: { type: Number, default: 21 },
    licenseRequirement: {
      type: String,
      default: 'صالح لمدة سنة على الأقل'
    },
    fuelPolicy: {
      type: String,
      enum: ['full-to-full', 'full-to-empty', 'same-to-same'],
      default: 'full-to-full'
    },
    cancellation: {
      freeCancellation: { type: Number, default: 24 }, // hours before pickup
      cancellationFee: { type: Number, default: 0 }
    },
    mileage: {
      unlimited: { type: Boolean, default: true },
      dailyLimit: Number,
      extraMileageFee: Number
    }
  },
  contact: {
    phone: String,
    email: String,
    website: String,
    emergencyPhone: String
  },
  operatingHours: {
    weekdays: {
      open: { type: String, default: '08:00' },
      close: { type: String, default: '18:00' }
    },
    weekends: {
      open: { type: String, default: '09:00' },
      close: { type: String, default: '17:00' }
    },
    holidays: {
      open: { type: String, default: '10:00' },
      close: { type: String, default: '16:00' }
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isPartner: {
    type: Boolean,
    default: false
  },
  partnerDiscount: {
    type: Number,
    min: 0,
    max: 50,
    default: 0
  }
}, {
  timestamps: true
});

// Index for efficient searching
carRentalSchema.index({ destination: 1, isActive: 1 });
carRentalSchema.index({ 'company.name': 1 });
carRentalSchema.index({ 'vehicles.category': 1 });
carRentalSchema.index({ isPartner: -1, partnerDiscount: -1 });

module.exports = mongoose.model('CarRental', carRentalSchema);
