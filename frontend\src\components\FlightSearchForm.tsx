import React, { useState } from 'react'

const FlightSearchForm: React.FC = () => {
  const [tripType, setTripType] = useState('roundtrip')
  const [from, setFrom] = useState('')
  const [to, setTo] = useState('')
  const [departDate, setDepartDate] = useState('')
  const [returnDate, setReturnDate] = useState('')
  const [passengers, setPassengers] = useState(1)

  return (
    <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-6xl mx-auto -mt-20 relative z-10">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4 text-center">ابحث عن رحلتك المثالية</h2>
        
        {/* Trip Type */}
        <div className="flex justify-center space-x-4 rtl:space-x-reverse mb-6">
          <label className="flex items-center">
            <input
              type="radio"
              value="roundtrip"
              checked={tripType === 'roundtrip'}
              onChange={(e) => setTripType(e.target.value)}
              className="mr-2 ml-2"
            />
            <span className="text-gray-700">ذهاب وعودة</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              value="oneway"
              checked={tripType === 'oneway'}
              onChange={(e) => setTripType(e.target.value)}
              className="mr-2 ml-2"
            />
            <span className="text-gray-700">ذهاب فقط</span>
          </label>
        </div>
      </div>

      {/* Search Form */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* From */}
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-2">من</label>
          <div className="relative">
            <span className="absolute left-3 top-3 text-skyway-500">✈️</span>
            <input
              type="text"
              value={from}
              onChange={(e) => setFrom(e.target.value)}
              placeholder="مدينة المغادرة"
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-skyway-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* To */}
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-2">إلى</label>
          <div className="relative">
            <span className="absolute left-3 top-3 text-skyway-500">🏙️</span>
            <input
              type="text"
              value={to}
              onChange={(e) => setTo(e.target.value)}
              placeholder="مدينة الوصول"
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-skyway-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Depart Date */}
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ المغادرة</label>
          <div className="relative">
            <span className="absolute left-3 top-3 text-skyway-500">📅</span>
            <input
              type="date"
              value={departDate}
              onChange={(e) => setDepartDate(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-skyway-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Return Date */}
        {tripType === 'roundtrip' && (
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ العودة</label>
            <div className="relative">
              <span className="absolute left-3 top-3 text-skyway-500">📅</span>
              <input
                type="date"
                value={returnDate}
                onChange={(e) => setReturnDate(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-skyway-500 focus:border-transparent"
              />
            </div>
          </div>
        )}

        {/* Passengers */}
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-2">المسافرون</label>
          <div className="relative">
            <span className="absolute left-3 top-3 text-skyway-500">👥</span>
            <select
              value={passengers}
              onChange={(e) => setPassengers(Number(e.target.value))}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-skyway-500 focus:border-transparent"
            >
              {[1, 2, 3, 4, 5, 6, 7, 8].map(num => (
                <option key={num} value={num}>{num} {num === 1 ? 'مسافر' : 'مسافرين'}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Search Button */}
      <div className="text-center">
        <button className="bg-gradient-to-r from-skyway-600 to-skyway-700 text-white px-12 py-4 rounded-lg font-bold text-lg hover:from-skyway-700 hover:to-skyway-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
          🔍 ابحث عن الرحلات
        </button>
      </div>
    </div>
  )
}

export default FlightSearchForm
