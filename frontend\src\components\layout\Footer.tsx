import React from 'react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-800 text-white py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">طرق السماء</h3>
            <p className="text-gray-300 text-sm">
              رحلات استثنائية إلى أجمل الوجهات حول العالم
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold mb-4">روابط سريعة</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="/" className="text-gray-300 hover:text-white transition-colors">الرئيسية</a></li>
              <li><a href="/about" className="text-gray-300 hover:text-white transition-colors">من نحن</a></li>
              <li><a href="/contact" className="text-gray-300 hover:text-white transition-colors">اتصل بنا</a></li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="font-semibold mb-4">خدماتنا</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="/plan-book" className="text-gray-300 hover:text-white transition-colors">خطط واحجز</a></li>
              <li><a href="/prepare" className="text-gray-300 hover:text-white transition-colors">استعد للرحلة</a></li>
              <li><a href="/nasmiles" className="text-gray-300 hover:text-white transition-colors">nasmiles</a></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="font-semibold mb-4">تواصل معنا</h4>
            <div className="space-y-2 text-sm text-gray-300">
              <p>📞 +966 11 123 4567</p>
              <p>✉️ <EMAIL></p>
              <p>📍 الرياض، المملكة العربية السعودية</p>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-400">
          <p>&copy; 2024 طرق السماء - FlyWay. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
