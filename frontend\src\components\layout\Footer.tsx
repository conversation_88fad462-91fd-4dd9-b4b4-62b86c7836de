import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  Plane, 
  Mail, 
  Phone, 
  MapPin, 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin,
  Youtube,
  Send
} from 'lucide-react';

const Footer: React.FC = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const footerSections = [
    {
      title: t('planAndBook.title'),
      links: [
        { label: t('planAndBook.planJourney.title'), href: '/plan-journey' },
        { label: t('planAndBook.book.flights'), href: '/flights' },
        { label: t('planAndBook.book.hotels'), href: '/hotels' },
        { label: t('planAndBook.book.cars'), href: '/cars' },
        { label: t('planAndBook.beforeFly.title'), href: '/before-fly' }
      ]
    },
    {
      title: t('prepareTrip.title'),
      links: [
        { label: t('prepareTrip.baggageInfo.title'), href: '/baggage-info' },
        { label: t('prepareTrip.manageBooking.title'), href: '/manage-booking' },
        { label: t('prepareTrip.enhanceTrip.title'), href: '/enhance-trip' },
        { label: t('prepareTrip.manageBooking.checkIn'), href: '/check-in' }
      ]
    },
    {
      title: t('information.title'),
      links: [
        { label: t('footer.aboutUs'), href: '/about' },
        { label: t('information.help.faq'), href: '/faq' },
        { label: t('footer.contactUs'), href: '/contact' },
        { label: t('information.specialServices.title'), href: '/special-services' },
        { label: t('footer.careers'), href: '/careers' }
      ]
    },
    {
      title: t('nasmiles.title'),
      links: [
        { label: t('nasmiles.earnPoints'), href: '/nasmiles/earn' },
        { label: t('nasmiles.redeemRewards'), href: '/nasmiles/redeem' },
        { label: t('nasmiles.tierBenefits'), href: '/nasmiles/benefits' },
        { label: t('nasmiles.partners'), href: '/nasmiles/partners' },
        { label: t('nasmiles.myAccount'), href: '/nasmiles/account' }
      ]
    }
  ];

  const socialLinks = [
    { icon: Facebook, href: '#', label: 'Facebook' },
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: Instagram, href: '#', label: 'Instagram' },
    { icon: Linkedin, href: '#', label: 'LinkedIn' },
    { icon: Youtube, href: '#', label: 'YouTube' }
  ];

  return (
    <footer className="bg-secondary-900 text-white">
      {/* Newsletter Section */}
      <div className="border-b border-secondary-800">
        <div className="container-custom py-12">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="heading-3 text-white mb-4">
              {t('footer.newsletter')}
            </h3>
            <p className="text-body text-secondary-300 mb-8">
              {isRTL 
                ? 'اشترك في نشرتنا الإخبارية للحصول على أفضل العروض والأخبار'
                : 'Subscribe to our newsletter for the best deals and news'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <div className="flex-1">
                <input
                  type="email"
                  placeholder={isRTL ? 'البريد الإلكتروني' : 'Email address'}
                  className="w-full px-4 py-3 bg-secondary-800 border border-secondary-700 rounded-lg text-white placeholder-secondary-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                />
              </div>
              <button className="btn-primary px-6 py-3 flex items-center justify-center space-x-2 rtl:space-x-reverse">
                <Send className="w-4 h-4" />
                <span>{t('footer.subscribe')}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container-custom py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <Link to="/" className="flex items-center space-x-3 rtl:space-x-reverse mb-6">
              <div className="flex items-center justify-center w-12 h-12 gradient-primary rounded-xl">
                <Plane className="w-7 h-7 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">
                  {isRTL ? 'طرق السماء' : 'FlyWay'}
                </h2>
                <p className="text-sm text-secondary-400">
                  {isRTL ? 'رحلات استثنائية' : 'Exceptional Journeys'}
                </p>
              </div>
            </Link>
            
            <p className="text-secondary-300 mb-6 leading-relaxed">
              {isRTL 
                ? 'نحن نقدم أفضل خدمات الطيران مع تجربة سفر استثنائية وأسعار تنافسية لجميع الوجهات حول العالم.'
                : 'We provide the best aviation services with exceptional travel experience and competitive prices to all destinations around the world.'
              }
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 rtl:space-x-reverse text-secondary-300">
                <Phone className="w-4 h-4 text-primary-400" />
                <span>+966 11 123 4567</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse text-secondary-300">
                <Mail className="w-4 h-4 text-primary-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse text-secondary-300">
                <MapPin className="w-4 h-4 text-primary-400" />
                <span>{isRTL ? 'الرياض، المملكة العربية السعودية' : 'Riyadh, Saudi Arabia'}</span>
              </div>
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section, index) => (
            <div key={index} className="lg:col-span-1">
              <h4 className="text-lg font-semibold text-white mb-4">
                {section.title}
              </h4>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      to={link.href}
                      className="text-secondary-300 hover:text-primary-400 transition-colors duration-200 text-sm"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Social Media */}
        <div className="mt-12 pt-8 border-t border-secondary-800">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0">
              <h4 className="text-lg font-semibold text-white mb-4">
                {t('footer.followUs')}
              </h4>
              <div className="flex space-x-4 rtl:space-x-reverse">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    className="flex items-center justify-center w-10 h-10 bg-secondary-800 hover:bg-primary-600 rounded-lg transition-colors duration-200 group"
                    aria-label={social.label}
                  >
                    <social.icon className="w-5 h-5 text-secondary-400 group-hover:text-white" />
                  </a>
                ))}
              </div>
            </div>

            {/* App Download */}
            <div className="text-center md:text-right rtl:md:text-left">
              <h4 className="text-lg font-semibold text-white mb-4">
                {isRTL ? 'حمل التطبيق' : 'Download App'}
              </h4>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 rtl:sm:space-x-reverse">
                <a href="#" className="inline-flex items-center px-4 py-2 bg-secondary-800 hover:bg-secondary-700 rounded-lg transition-colors duration-200">
                  <img src="/images/app-store.png" alt="App Store" className="h-8" />
                </a>
                <a href="#" className="inline-flex items-center px-4 py-2 bg-secondary-800 hover:bg-secondary-700 rounded-lg transition-colors duration-200">
                  <img src="/images/google-play.png" alt="Google Play" className="h-8" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-secondary-800 bg-secondary-950">
        <div className="container-custom py-6">
          <div className="flex flex-col md:flex-row justify-between items-center text-sm text-secondary-400">
            <div className="mb-4 md:mb-0">
              <p>&copy; 2024 {t('footer.copyright')}</p>
            </div>
            <div className="flex flex-wrap justify-center md:justify-end space-x-6 rtl:space-x-reverse">
              <Link to="/privacy" className="hover:text-primary-400 transition-colors duration-200">
                {t('footer.privacy')}
              </Link>
              <Link to="/terms" className="hover:text-primary-400 transition-colors duration-200">
                {t('footer.terms')}
              </Link>
              <Link to="/sitemap" className="hover:text-primary-400 transition-colors duration-200">
                {t('footer.sitemap')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
