import React from 'react'
import Header from './components/Header'
import Footer from './components/Footer'
import FlightSearchForm from './components/FlightSearchForm'
import PopularDestinations from './components/PopularDestinations'
import ServicesSection from './components/ServicesSection'
import FeaturesSection from './components/FeaturesSection'

function App() {
  return (
    <div className="min-h-screen font-cairo">
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-skyway-100 py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-7xl font-bold text-skyway-800 mb-6">
              SkyWay طرق السماء
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-12">
              رحلات استثنائية إلى أجمل الوجهات حول العالم بأفضل الأسعار والخدمات المميزة
            </p>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <div className="text-3xl font-bold text-skyway-600 mb-2">15M+</div>
                <div className="text-gray-600">مسافر سنوياً</div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <div className="text-3xl font-bold text-skyway-600 mb-2">85+</div>
                <div className="text-gray-600">وجهة حول العالم</div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <div className="text-3xl font-bold text-skyway-600 mb-2">120+</div>
                <div className="text-gray-600">طائرة حديثة</div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <div className="text-3xl font-bold text-skyway-600 mb-2">96%</div>
                <div className="text-gray-600">رضا العملاء</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Flight Search Form */}
      <section className="relative">
        <div className="container mx-auto px-4">
          <FlightSearchForm />
        </div>
      </section>

      {/* Popular Destinations */}
      <PopularDestinations />

      {/* Services Section */}
      <ServicesSection />

      {/* Features Section */}
      <FeaturesSection />

      <Footer />
    </div>
  )
}

export default App
