<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlyWay - طرق السماء | اختبار الواجهة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom animations */
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        
        .animate-bounce-soft {
            animation: bounce-soft 3s ease-in-out infinite;
        }
        
        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        @keyframes bounce-soft {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
        <!-- Background Image with Overlay -->
        <div class="absolute inset-0 z-0">
            <img
                src="https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                alt="Hero Background"
                class="w-full h-full object-cover"
            />
            <div class="absolute inset-0 bg-gradient-to-br from-blue-900/80 via-blue-800/70 to-orange-600/60"></div>
            <div class="absolute inset-0 bg-black/20"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute inset-0 z-10">
            <div class="absolute top-20 left-10 w-16 h-16 bg-white/10 rounded-full backdrop-blur-sm animate-float"></div>
            <div class="absolute top-40 right-20 w-12 h-12 bg-orange-400/20 rounded-full backdrop-blur-sm animate-bounce-soft"></div>
            <div class="absolute bottom-40 left-20 w-20 h-20 bg-blue-400/15 rounded-full backdrop-blur-sm animate-float"></div>
        </div>

        <!-- Main Content -->
        <div class="relative z-20 container mx-auto px-4">
            <div class="text-center mb-12">
                <!-- Hero Title -->
                <div class="mb-8 animate-fade-in-up">
                    <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold text-white leading-tight mb-6">
                        <span class="block">اكتشف العالم معنا</span>
                        <span class="block text-orange-300">طرق السماء</span>
                    </h1>
                    
                    <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                        رحلات استثنائية إلى أجمل الوجهات حول العالم بأفضل الأسعار والخدمات
                    </p>
                </div>

                <!-- Stats -->
                <div class="flex flex-wrap justify-center gap-8 mb-12 animate-fade-in-up">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">15M+</div>
                        <div class="text-white/80 text-sm md:text-base">مسافر سنوياً</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">85+</div>
                        <div class="text-white/80 text-sm md:text-base">وجهة</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">120+</div>
                        <div class="text-white/80 text-sm md:text-base">طائرة</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">96%</div>
                        <div class="text-white/80 text-sm md:text-base">رضا العملاء</div>
                    </div>
                </div>
            </div>

            <!-- Flight Search Form -->
            <div class="max-w-6xl mx-auto animate-fade-in-up">
                <div class="glass-effect rounded-3xl p-6 lg:p-8 shadow-2xl">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">ابحث عن رحلتك</h2>
                    
                    <!-- Trip Type Selector -->
                    <div class="flex flex-wrap gap-2 mb-6">
                        <button class="px-4 py-2 rounded-lg text-sm font-medium bg-blue-600 text-white shadow-md">
                            ذهاب وعودة
                        </button>
                        <button class="px-4 py-2 rounded-lg text-sm font-medium bg-white/50 text-gray-700 hover:bg-white/70">
                            ذهاب فقط
                        </button>
                        <button class="px-4 py-2 rounded-lg text-sm font-medium bg-white/50 text-gray-700 hover:bg-white/70">
                            متعدد المدن
                        </button>
                    </div>

                    <!-- Search Form -->
                    <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">من</label>
                            <input type="text" placeholder="من أين؟" class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">إلى</label>
                            <input type="text" placeholder="إلى أين؟" class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ المغادرة</label>
                            <input type="date" class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المسافرون</label>
                            <select class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option>1 مسافر</option>
                                <option>2 مسافر</option>
                                <option>3 مسافر</option>
                                <option>4+ مسافر</option>
                            </select>
                        </div>
                    </div>

                    <!-- Search Button -->
                    <div class="text-center">
                        <button class="bg-gradient-to-r from-blue-600 to-orange-500 text-white px-8 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            ابحث عن الرحلات
                        </button>
                    </div>
                </div>
            </div>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mt-12 animate-fade-in-up">
                <button class="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-8 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                    احجز رحلتك الآن
                </button>
                <button class="border-2 border-white text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                    استكشف الوجهات
                </button>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 animate-bounce">
            <div class="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white/70 rounded-full mt-2"></div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-6">لماذا تختار طرق السماء؟</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    نحن نقدم تجربة سفر استثنائية مع أفضل الخدمات والمميزات
                </p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center group">
                    <div class="w-20 h-20 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <div class="w-10 h-10 bg-blue-600 rounded-full"></div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">حجز سريع</h3>
                    <p class="text-gray-600">احجز رحلتك في أقل من 3 دقائق</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <div class="w-10 h-10 bg-green-600 rounded-full"></div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">دفع آمن</h3>
                    <p class="text-gray-600">حماية كاملة لبياناتك المالية</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-yellow-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <div class="w-10 h-10 bg-yellow-600 rounded-full"></div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">أفضل الأسعار</h3>
                    <p class="text-gray-600">ضمان أفضل الأسعار في السوق</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <div class="w-10 h-10 bg-purple-600 rounded-full"></div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">دعم 24/7</h3>
                    <p class="text-gray-600">فريق دعم متاح على مدار الساعة</p>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            console.log('FlyWay - طرق السماء loaded successfully!');
            
            // Add click handlers for buttons
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('Button clicked:', this.textContent);
                });
            });
        });
    </script>
</body>
</html>
