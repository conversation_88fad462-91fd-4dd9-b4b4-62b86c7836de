@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-secondary-900 bg-white;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Arabic font support */
  [dir="rtl"] {
    @apply font-arabic;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-md hover:shadow-lg;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-100 text-secondary-700 hover:bg-secondary-200 focus:ring-secondary-500;
  }
  
  .btn-accent {
    @apply btn bg-accent-500 text-white hover:bg-accent-600 focus:ring-accent-500 shadow-md hover:shadow-lg;
  }
  
  .btn-outline {
    @apply btn border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-secondary-600 hover:bg-secondary-100 focus:ring-secondary-500;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-xl {
    @apply px-8 py-4 text-lg;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-secondary-100 overflow-hidden;
  }
  
  .card-hover {
    @apply card transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
  }
  
  .card-interactive {
    @apply card-hover cursor-pointer hover:border-primary-200;
  }
  
  /* Input Components */
  .input {
    @apply w-full px-4 py-3 text-sm border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .input-lg {
    @apply input py-4 text-base;
  }
  
  .input-error {
    @apply input border-error-500 focus:ring-error-500 focus:border-error-500;
  }
  
  /* Layout Components */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-16 lg:py-24;
  }
  
  .section-padding-sm {
    @apply py-8 lg:py-12;
  }
  
  /* Text Components */
  .heading-1 {
    @apply text-4xl lg:text-5xl xl:text-6xl font-bold text-secondary-900 leading-tight;
  }
  
  .heading-2 {
    @apply text-3xl lg:text-4xl font-bold text-secondary-900 leading-tight;
  }
  
  .heading-3 {
    @apply text-2xl lg:text-3xl font-semibold text-secondary-900 leading-tight;
  }
  
  .heading-4 {
    @apply text-xl lg:text-2xl font-semibold text-secondary-900 leading-tight;
  }
  
  .text-body {
    @apply text-base text-secondary-600 leading-relaxed;
  }
  
  .text-body-lg {
    @apply text-lg text-secondary-600 leading-relaxed;
  }
  
  /* Gradient Components */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700;
  }
  
  .gradient-accent {
    @apply bg-gradient-to-r from-accent-500 to-accent-600;
  }
  
  .gradient-hero {
    @apply bg-gradient-to-br from-primary-600 via-primary-700 to-accent-600;
  }
  
  /* Animation Components */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  .animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out forwards;
  }
  
  .animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out forwards;
  }
  
  .animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out forwards;
  }
  
  /* Loading Components */
  .loading-spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
  }
  
  .loading-dots {
    @apply inline-flex space-x-1;
  }
  
  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-bounce;
  }
  
  .loading-dots > div:nth-child(2) {
    animation-delay: 0.1s;
  }
  
  .loading-dots > div:nth-child(3) {
    animation-delay: 0.2s;
  }
}

@layer utilities {
  /* RTL Support */
  .rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }
  
  /* Custom Utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent;
  }
  
  .backdrop-blur-glass {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
  }
  
  .glass-effect {
    @apply backdrop-blur-glass border border-white/20 shadow-large;
  }
  
  /* Hover Effects */
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-2;
  }
  
  .hover-glow {
    @apply transition-shadow duration-300 hover:shadow-glow;
  }
  
  /* Focus States */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  /* Responsive Text */
  .responsive-text {
    @apply text-sm sm:text-base lg:text-lg;
  }
}

/* Custom Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
}
