@echo off
echo ========================================
echo    FlyWay - طرق السماء
echo    Restarting VS Code to Clear Cache
echo ========================================
echo.

echo Closing VS Code...
taskkill /f /im "Code.exe" 2>nul

echo Waiting for VS Code to close...
timeout /t 3 /nobreak >nul

echo Clearing VS Code cache...
if exist "%APPDATA%\Code\User\workspaceStorage" (
    echo Clearing workspace storage...
    rd /s /q "%APPDATA%\Code\User\workspaceStorage" 2>nul
)

if exist "%APPDATA%\Code\CachedExtensions" (
    echo Clearing cached extensions...
    rd /s /q "%APPDATA%\Code\CachedExtensions" 2>nul
)

echo.
echo Restarting VS Code...
start "" "code" "."

echo.
echo ✅ VS Code restarted successfully!
echo The frontend folder has been completely removed.
echo All 33 errors should now be cleared.
echo.
pause
