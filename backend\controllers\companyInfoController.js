const { CompanyInfo, Fleet, Sustainability } = require('../models/CompanyInfo');
const { asyncHandler } = require('../middlewares/errorHandler');

// @desc    Get company information by section
// @route   GET /api/company/:section
// @access  Public
const getCompanyInfoBySection = asyncHandler(async (req, res) => {
  const { section } = req.params;
  
  const validSections = ['about', 'fleet', 'sustainability', 'careers', 'news', 'awards'];
  
  if (!validSections.includes(section)) {
    return res.status(400).json({
      success: false,
      message: 'قسم غير صحيح'
    });
  }

  const companyInfo = await CompanyInfo.findOne({
    section,
    isActive: true
  });

  if (!companyInfo) {
    return res.status(404).json({
      success: false,
      message: 'المعلومات غير متوفرة'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      companyInfo
    }
  });
});

// @desc    Get all company sections
// @route   GET /api/company
// @access  Public
const getAllCompanySections = asyncHandler(async (req, res) => {
  const sections = await CompanyInfo.find({
    isActive: true
  }).sort({ order: 1 }).select('section title summary images order');

  res.status(200).json({
    success: true,
    data: {
      sections
    }
  });
});

// @desc    Get fleet information
// @route   GET /api/company/fleet/details
// @access  Public
const getFleetDetails = asyncHandler(async (req, res) => {
  const fleet = await Fleet.findOne().sort({ createdAt: -1 });

  if (!fleet) {
    // Return default fleet data if none exists
    const defaultFleet = {
      aircraft: [
        {
          model: 'Boeing 777-300ER',
          manufacturer: 'Boeing',
          quantity: 12,
          capacity: { economy: 350, business: 42, first: 8, total: 400 },
          range: 13649,
          speed: 905,
          features: ['WiFi', 'Entertainment System', 'Power Outlets'],
          yearIntroduced: 2020,
          isActive: true
        },
        {
          model: 'Airbus A350-900',
          manufacturer: 'Airbus',
          quantity: 8,
          capacity: { economy: 300, business: 36, first: 6, total: 342 },
          range: 15000,
          speed: 903,
          features: ['WiFi', 'Entertainment System', 'Premium Cabin'],
          yearIntroduced: 2021,
          isActive: true
        }
      ],
      totalAircraft: 20,
      averageAge: 3.5,
      destinations: 85,
      dailyFlights: 150
    };

    return res.status(200).json({
      success: true,
      data: {
        fleet: defaultFleet
      }
    });
  }

  res.status(200).json({
    success: true,
    data: {
      fleet
    }
  });
});

// @desc    Get sustainability information
// @route   GET /api/company/sustainability/details
// @access  Public
const getSustainabilityDetails = asyncHandler(async (req, res) => {
  const sustainability = await Sustainability.findOne().sort({ createdAt: -1 });

  if (!sustainability) {
    // Return default sustainability data
    const defaultSustainability = {
      initiatives: [
        {
          title: {
            ar: 'تقليل انبعاثات الكربون',
            en: 'Carbon Emission Reduction'
          },
          description: {
            ar: 'برنامج شامل لتقليل انبعاثات الكربون بنسبة 50% بحلول 2030',
            en: 'Comprehensive program to reduce carbon emissions by 50% by 2030'
          },
          category: 'carbon-offset',
          impact: {
            metric: 'CO2 Reduction',
            value: '25%',
            unit: 'since 2020'
          },
          status: 'active'
        },
        {
          title: {
            ar: 'كفاءة الوقود',
            en: 'Fuel Efficiency'
          },
          description: {
            ar: 'استخدام أحدث التقنيات لتحسين كفاءة استهلاك الوقود',
            en: 'Using latest technologies to improve fuel consumption efficiency'
          },
          category: 'fuel-efficiency',
          impact: {
            metric: 'Fuel Savings',
            value: '15%',
            unit: 'annually'
          },
          status: 'active'
        }
      ],
      goals: [
        {
          target: {
            ar: 'الوصول للحياد الكربوني بحلول 2050',
            en: 'Achieve carbon neutrality by 2050'
          },
          deadline: new Date('2050-12-31'),
          progress: 35,
          status: 'on-track'
        }
      ]
    };

    return res.status(200).json({
      success: true,
      data: {
        sustainability: defaultSustainability
      }
    });
  }

  res.status(200).json({
    success: true,
    data: {
      sustainability
    }
  });
});

// @desc    Get company statistics
// @route   GET /api/company/statistics
// @access  Public
const getCompanyStatistics = asyncHandler(async (req, res) => {
  const statistics = [
    {
      label: { ar: 'المسافرون سنوياً', en: 'Annual Passengers' },
      value: '15M+',
      icon: 'users',
      order: 1
    },
    {
      label: { ar: 'الوجهات', en: 'Destinations' },
      value: '85+',
      icon: 'map-pin',
      order: 2
    },
    {
      label: { ar: 'الطائرات', en: 'Aircraft Fleet' },
      value: '120+',
      icon: 'plane',
      order: 3
    },
    {
      label: { ar: 'سنوات الخبرة', en: 'Years of Experience' },
      value: '25+',
      icon: 'award',
      order: 4
    },
    {
      label: { ar: 'الموظفون', en: 'Employees' },
      value: '8,500+',
      icon: 'briefcase',
      order: 5
    },
    {
      label: { ar: 'معدل الرضا', en: 'Satisfaction Rate' },
      value: '96%',
      icon: 'star',
      order: 6
    }
  ];

  res.status(200).json({
    success: true,
    data: {
      statistics
    }
  });
});

// @desc    Get company timeline
// @route   GET /api/company/timeline
// @access  Public
const getCompanyTimeline = asyncHandler(async (req, res) => {
  const timeline = [
    {
      year: 1999,
      event: { ar: 'تأسيس الشركة', en: 'Company Founded' },
      description: { ar: 'بداية رحلة FlyWay مع رحلتين يومياً', en: 'FlyWay journey begins with two daily flights' }
    },
    {
      year: 2005,
      event: { ar: 'التوسع الإقليمي', en: 'Regional Expansion' },
      description: { ar: 'بداية الرحلات الإقليمية في الشرق الأوسط', en: 'Started regional flights in Middle East' }
    },
    {
      year: 2010,
      event: { ar: 'الرحلات الدولية', en: 'International Routes' },
      description: { ar: 'إطلاق أول رحلة دولية إلى أوروبا', en: 'Launched first international flight to Europe' }
    },
    {
      year: 2015,
      event: { ar: 'برنامج الولاء', en: 'Loyalty Program' },
      description: { ar: 'إطلاق برنامج nasmiles للولاء', en: 'Launched nasmiles loyalty program' }
    },
    {
      year: 2020,
      event: { ar: 'التحول الرقمي', en: 'Digital Transformation' },
      description: { ar: 'إطلاق منصة الحجز الرقمية الجديدة', en: 'Launched new digital booking platform' }
    },
    {
      year: 2024,
      event: { ar: 'الاستدامة', en: 'Sustainability Initiative' },
      description: { ar: 'إطلاق برنامج الاستدامة البيئية', en: 'Launched environmental sustainability program' }
    }
  ];

  res.status(200).json({
    success: true,
    data: {
      timeline
    }
  });
});

// @desc    Create company info (Admin only)
// @route   POST /api/company
// @access  Private/Admin
const createCompanyInfo = asyncHandler(async (req, res) => {
  const companyInfo = await CompanyInfo.create(req.body);

  res.status(201).json({
    success: true,
    message: 'تم إنشاء معلومات الشركة بنجاح',
    data: {
      companyInfo
    }
  });
});

// @desc    Update company info (Admin only)
// @route   PUT /api/company/:id
// @access  Private/Admin
const updateCompanyInfo = asyncHandler(async (req, res) => {
  const companyInfo = await CompanyInfo.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  );

  if (!companyInfo) {
    return res.status(404).json({
      success: false,
      message: 'معلومات الشركة غير موجودة'
    });
  }

  res.status(200).json({
    success: true,
    message: 'تم تحديث معلومات الشركة بنجاح',
    data: {
      companyInfo
    }
  });
});

// @desc    Delete company info (Admin only)
// @route   DELETE /api/company/:id
// @access  Private/Admin
const deleteCompanyInfo = asyncHandler(async (req, res) => {
  const companyInfo = await CompanyInfo.findById(req.params.id);

  if (!companyInfo) {
    return res.status(404).json({
      success: false,
      message: 'معلومات الشركة غير موجودة'
    });
  }

  companyInfo.isActive = false;
  await companyInfo.save();

  res.status(200).json({
    success: true,
    message: 'تم حذف معلومات الشركة بنجاح'
  });
});

module.exports = {
  getCompanyInfoBySection,
  getAllCompanySections,
  getFleetDetails,
  getSustainabilityDetails,
  getCompanyStatistics,
  getCompanyTimeline,
  createCompanyInfo,
  updateCompanyInfo,
  deleteCompanyInfo
};
