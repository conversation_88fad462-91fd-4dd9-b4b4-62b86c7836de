import React from 'react';

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="animate-fade-in-up">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-6">
              مرحباً بك في طرق السماء
            </h1>
            <h2 className="text-2xl md:text-4xl font-semibold text-blue-600 mb-8">
              FlyWay
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-12">
              رحلات استثنائية إلى أجمل الوجهات حول العالم بأفضل الأسعار والخدمات المميزة
            </p>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                ابحث عن رحلات
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                استكشف الوجهات
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Stats */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div className="animate-fade-in-up">
              <div className="text-3xl font-bold text-blue-600 mb-2">15M+</div>
              <div className="text-gray-600">مسافر سنوياً</div>
            </div>
            <div className="animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
              <div className="text-3xl font-bold text-blue-600 mb-2">85+</div>
              <div className="text-gray-600">وجهة حول العالم</div>
            </div>
            <div className="animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <div className="text-3xl font-bold text-blue-600 mb-2">120+</div>
              <div className="text-gray-600">طائرة حديثة</div>
            </div>
            <div className="animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
              <div className="text-3xl font-bold text-blue-600 mb-2">96%</div>
              <div className="text-gray-600">رضا العملاء</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
