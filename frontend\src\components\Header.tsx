import React, { useState } from 'react'

const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-gradient-to-br from-skyway-500 to-skyway-700 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-xl">✈</span>
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <span className="text-2xl font-bold text-skyway-800">SkyWay</span>
              <span className="text-lg text-gray-600">طرق السماء</span>
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            <a href="#" className="text-gray-700 hover:text-skyway-600 transition-colors font-medium">
              الرئيسية
            </a>
            <a href="#" className="text-gray-700 hover:text-skyway-600 transition-colors font-medium">
              خطط واحجز
            </a>
            <a href="#" className="text-gray-700 hover:text-skyway-600 transition-colors font-medium">
              استعد للرحلة
            </a>
            <a href="#" className="text-gray-700 hover:text-skyway-600 transition-colors font-medium">
              معلومات
            </a>
            <a href="#" className="text-gray-700 hover:text-skyway-600 transition-colors font-medium">
              nasmiles
            </a>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {/* Language Toggle */}
            <button className="hidden md:flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 hover:text-skyway-600 transition-colors">
              <span>🌐</span>
              <span>العربية</span>
            </button>
            
            {/* Login Button */}
            <button className="hidden md:block bg-skyway-600 text-white px-6 py-2 rounded-lg text-sm font-medium hover:bg-skyway-700 transition-colors">
              تسجيل الدخول
            </button>
            
            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2 text-gray-600 hover:text-skyway-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
        
        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-100 bg-white mt-4 pt-4">
            <div className="space-y-4">
              <a href="#" className="block text-gray-700 hover:text-skyway-600 transition-colors font-medium">
                الرئيسية
              </a>
              <a href="#" className="block text-gray-700 hover:text-skyway-600 transition-colors font-medium">
                خطط واحجز
              </a>
              <a href="#" className="block text-gray-700 hover:text-skyway-600 transition-colors font-medium">
                استعد للرحلة
              </a>
              <a href="#" className="block text-gray-700 hover:text-skyway-600 transition-colors font-medium">
                معلومات
              </a>
              <a href="#" className="block text-gray-700 hover:text-skyway-600 transition-colors font-medium">
                nasmiles
              </a>
              
              <div className="pt-4 border-t border-gray-100 space-y-3">
                <button className="flex items-center space-x-2 rtl:space-x-reverse text-gray-600">
                  <span>🌐</span>
                  <span className="text-sm">العربية</span>
                </button>
                <button className="w-full bg-skyway-600 text-white py-2 rounded-lg text-sm font-medium hover:bg-skyway-700 transition-colors">
                  تسجيل الدخول
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
