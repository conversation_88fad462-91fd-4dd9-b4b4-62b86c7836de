import React from 'react'

const ServicesSection: React.FC = () => {
  const services = [
    {
      id: 1,
      title: 'خطط واحجز',
      description: 'خطط لرحلتك المثالية واحجز بأفضل الأسعار مع ضمان أفضل العروض',
      icon: '🗓️',
      features: ['مقارنة الأسعار', 'حجز فوري', 'ضمان أفضل سعر', 'إلغاء مجاني']
    },
    {
      id: 2,
      title: 'استعد للرحلة',
      description: 'كل ما تحتاجه للاستعداد لرحلتك من معلومات السفر إلى النصائح المفيدة',
      icon: '🧳',
      features: ['معلومات الوجهة', 'نصائح السفر', 'قائمة التحضيرات', 'الطقس والمناخ']
    },
    {
      id: 3,
      title: 'معلومات',
      description: 'احصل على جميع المعلومات التي تحتاجها حول رحلتك ووجهتك',
      icon: 'ℹ️',
      features: ['دليل الوجهات', 'معلومات الطيران', 'الأسئلة الشائعة', 'دعم العملاء']
    },
    {
      id: 4,
      title: 'nasmiles',
      description: 'برنامج الولاء الخاص بنا لتجميع النقاط والحصول على مكافآت حصرية',
      icon: '⭐',
      features: ['تجميع النقاط', 'مكافآت حصرية', 'ترقيات مجانية', 'خصومات خاصة']
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">خدماتنا المميزة</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            نقدم لك مجموعة شاملة من الخدمات لضمان رحلة مريحة وممتعة
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service) => (
            <div key={service.id} className="bg-gradient-to-br from-skyway-50 to-blue-50 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-skyway-100">
              <div className="text-center mb-6">
                <div className="text-5xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">{service.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{service.description}</p>
              </div>

              <div className="space-y-3">
                {service.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                    <span className="text-skyway-600">✓</span>
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>

              <div className="mt-6">
                <button className="w-full bg-skyway-600 text-white py-3 rounded-lg font-semibold hover:bg-skyway-700 transition-colors">
                  اعرف المزيد
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default ServicesSection
