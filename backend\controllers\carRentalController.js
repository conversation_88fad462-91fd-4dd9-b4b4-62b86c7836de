const CarRental = require('../models/CarRental');
const Destination = require('../models/Destination');
const { asyncHandler } = require('../middlewares/errorHandler');

// @desc    Search car rentals
// @route   POST /api/car-rentals/search
// @access  Public
const searchCarRentals = asyncHandler(async (req, res) => {
  const {
    destination,
    pickupDate,
    returnDate,
    pickupLocation = 'airport',
    category,
    transmission,
    minPassengers = 1
  } = req.body;

  // Find destination
  const destinationDoc = await Destination.findOne({
    $or: [
      { city: new RegExp(destination, 'i') },
      { 'airport.code': destination.toUpperCase() }
    ]
  });

  if (!destinationDoc) {
    return res.status(404).json({
      success: false,
      message: 'الوجهة غير موجودة'
    });
  }

  // Build search query
  const searchQuery = {
    destination: destinationDoc._id,
    isActive: true
  };

  // Check location availability
  if (pickupLocation === 'airport') {
    searchQuery['location.airport.available'] = true;
  } else if (pickupLocation === 'city') {
    searchQuery['location.cityCenter.available'] = true;
  }

  let carRentals = await CarRental.find(searchQuery)
    .populate('destination', 'city country airport.code');

  // Filter vehicles based on criteria
  const filteredRentals = carRentals.map(rental => {
    let availableVehicles = rental.vehicles.filter(vehicle => {
      let matches = true;
      
      if (category) {
        matches = matches && vehicle.category === category;
      }
      
      if (transmission) {
        matches = matches && vehicle.transmission === transmission;
      }
      
      if (minPassengers) {
        matches = matches && vehicle.capacity.passengers >= minPassengers;
      }
      
      return matches && vehicle.availability > 0;
    });

    return {
      ...rental.toObject(),
      availableVehicles,
      hasAvailability: availableVehicles.length > 0
    };
  }).filter(rental => rental.hasAvailability);

  res.status(200).json({
    success: true,
    data: {
      searchCriteria: {
        destination: destinationDoc.city,
        pickupDate,
        returnDate,
        pickupLocation,
        category,
        transmission,
        minPassengers
      },
      rentals: filteredRentals,
      totalResults: filteredRentals.length
    }
  });
});

// @desc    Get car rental by ID
// @route   GET /api/car-rentals/:id
// @access  Public
const getCarRentalById = asyncHandler(async (req, res) => {
  const carRental = await CarRental.findById(req.params.id)
    .populate('destination', 'city country airport.code timezone currency');

  if (!carRental) {
    return res.status(404).json({
      success: false,
      message: 'شركة تأجير السيارات غير موجودة'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      carRental
    }
  });
});

// @desc    Get car rentals by destination
// @route   GET /api/car-rentals/destination/:destinationId
// @access  Public
const getCarRentalsByDestination = asyncHandler(async (req, res) => {
  const { destinationId } = req.params;
  const limit = parseInt(req.query.limit) || 10;

  const carRentals = await CarRental.find({
    destination: destinationId,
    isActive: true
  })
  .populate('destination', 'city country')
  .sort({ 'company.rating': -1, isPartner: -1 })
  .limit(limit);

  res.status(200).json({
    success: true,
    data: {
      carRentals,
      count: carRentals.length
    }
  });
});

// @desc    Get partner car rentals
// @route   GET /api/car-rentals/partners
// @access  Public
const getPartnerCarRentals = asyncHandler(async (req, res) => {
  const limit = parseInt(req.query.limit) || 8;

  const carRentals = await CarRental.find({
    isPartner: true,
    isActive: true
  })
  .populate('destination', 'city country')
  .sort({ partnerDiscount: -1, 'company.rating': -1 })
  .limit(limit)
  .select('company destination partnerDiscount vehicles.0');

  res.status(200).json({
    success: true,
    data: {
      carRentals
    }
  });
});

// @desc    Get vehicle categories
// @route   GET /api/car-rentals/categories
// @access  Public
const getVehicleCategories = asyncHandler(async (req, res) => {
  const categories = [
    {
      code: 'economy',
      name: { ar: 'اقتصادية', en: 'Economy' },
      description: { ar: 'سيارات صغيرة واقتصادية', en: 'Small and economical cars' },
      features: ['fuel-efficient', 'easy-parking', 'affordable'],
      typical: ['Toyota Yaris', 'Nissan Micra', 'Hyundai i10']
    },
    {
      code: 'compact',
      name: { ar: 'مدمجة', en: 'Compact' },
      description: { ar: 'سيارات متوسطة الحجم', en: 'Medium-sized cars' },
      features: ['comfortable', 'good-mileage', 'spacious'],
      typical: ['Toyota Corolla', 'Nissan Sentra', 'Hyundai Elantra']
    },
    {
      code: 'midsize',
      name: { ar: 'متوسطة', en: 'Midsize' },
      description: { ar: 'سيارات واسعة ومريحة', en: 'Spacious and comfortable cars' },
      features: ['spacious', 'comfortable', 'family-friendly'],
      typical: ['Toyota Camry', 'Nissan Altima', 'Honda Accord']
    },
    {
      code: 'luxury',
      name: { ar: 'فاخرة', en: 'Luxury' },
      description: { ar: 'سيارات فاخرة ومميزة', en: 'Premium luxury vehicles' },
      features: ['premium-interior', 'advanced-features', 'prestige'],
      typical: ['BMW 5 Series', 'Mercedes E-Class', 'Audi A6']
    },
    {
      code: 'suv',
      name: { ar: 'دفع رباعي', en: 'SUV' },
      description: { ar: 'سيارات دفع رباعي', en: 'Sport Utility Vehicles' },
      features: ['high-clearance', 'spacious', 'all-terrain'],
      typical: ['Toyota Prado', 'Nissan Patrol', 'Ford Explorer']
    }
  ];

  res.status(200).json({
    success: true,
    data: {
      categories
    }
  });
});

// @desc    Create car rental (Admin only)
// @route   POST /api/car-rentals
// @access  Private/Admin
const createCarRental = asyncHandler(async (req, res) => {
  const carRental = await CarRental.create(req.body);

  res.status(201).json({
    success: true,
    message: 'تم إنشاء شركة تأجير السيارات بنجاح',
    data: {
      carRental
    }
  });
});

// @desc    Update car rental (Admin only)
// @route   PUT /api/car-rentals/:id
// @access  Private/Admin
const updateCarRental = asyncHandler(async (req, res) => {
  const carRental = await CarRental.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  );

  if (!carRental) {
    return res.status(404).json({
      success: false,
      message: 'شركة تأجير السيارات غير موجودة'
    });
  }

  res.status(200).json({
    success: true,
    message: 'تم تحديث شركة تأجير السيارات بنجاح',
    data: {
      carRental
    }
  });
});

// @desc    Delete car rental (Admin only)
// @route   DELETE /api/car-rentals/:id
// @access  Private/Admin
const deleteCarRental = asyncHandler(async (req, res) => {
  const carRental = await CarRental.findById(req.params.id);

  if (!carRental) {
    return res.status(404).json({
      success: false,
      message: 'شركة تأجير السيارات غير موجودة'
    });
  }

  carRental.isActive = false;
  await carRental.save();

  res.status(200).json({
    success: true,
    message: 'تم حذف شركة تأجير السيارات بنجاح'
  });
});

module.exports = {
  searchCarRentals,
  getCarRentalById,
  getCarRentalsByDestination,
  getPartnerCarRentals,
  getVehicleCategories,
  createCarRental,
  updateCarRental,
  deleteCarRental
};
