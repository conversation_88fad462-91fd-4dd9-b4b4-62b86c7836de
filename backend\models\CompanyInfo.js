const mongoose = require('mongoose');

const companyInfoSchema = new mongoose.Schema({
  section: {
    type: String,
    enum: ['about', 'fleet', 'sustainability', 'careers', 'news', 'awards'],
    required: true,
    unique: true
  },
  title: {
    ar: { type: String, required: true },
    en: { type: String, required: true }
  },
  content: {
    ar: { type: String, required: true },
    en: { type: String, required: true }
  },
  summary: {
    ar: String,
    en: String
  },
  images: [{
    url: String,
    caption: {
      ar: String,
      en: String
    },
    alt: String
  }],
  statistics: [{
    label: {
      ar: String,
      en: String
    },
    value: String,
    icon: String,
    order: { type: Number, default: 0 }
  }],
  features: [{
    title: {
      ar: String,
      en: String
    },
    description: {
      ar: String,
      en: String
    },
    icon: String,
    order: { type: Number, default: 0 }
  }],
  timeline: [{
    year: Number,
    event: {
      ar: String,
      en: String
    },
    description: {
      ar: String,
      en: String
    }
  }],
  documents: [{
    name: {
      ar: String,
      en: String
    },
    url: String,
    type: {
      type: String,
      enum: ['pdf', 'doc', 'image', 'video']
    },
    size: String
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Fleet specific schema
const fleetSchema = new mongoose.Schema({
  aircraft: [{
    model: {
      type: String,
      required: true
    },
    manufacturer: {
      type: String,
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 0
    },
    capacity: {
      economy: Number,
      business: Number,
      first: Number,
      total: Number
    },
    range: {
      type: Number, // in kilometers
      required: true
    },
    speed: {
      type: Number, // in km/h
      required: true
    },
    features: [String],
    images: [String],
    specifications: {
      length: Number,
      wingspan: Number,
      height: Number,
      engines: String,
      fuelCapacity: Number
    },
    routes: [String], // typical routes for this aircraft
    yearIntroduced: Number,
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  totalAircraft: {
    type: Number,
    default: 0
  },
  averageAge: {
    type: Number,
    default: 0
  },
  destinations: {
    type: Number,
    default: 0
  },
  dailyFlights: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Sustainability specific schema
const sustainabilitySchema = new mongoose.Schema({
  initiatives: [{
    title: {
      ar: String,
      en: String
    },
    description: {
      ar: String,
      en: String
    },
    category: {
      type: String,
      enum: ['fuel-efficiency', 'carbon-offset', 'waste-reduction', 'community', 'innovation']
    },
    impact: {
      metric: String,
      value: String,
      unit: String
    },
    startDate: Date,
    status: {
      type: String,
      enum: ['planning', 'active', 'completed'],
      default: 'active'
    },
    images: [String]
  }],
  goals: [{
    target: {
      ar: String,
      en: String
    },
    deadline: Date,
    progress: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    status: {
      type: String,
      enum: ['on-track', 'behind', 'achieved'],
      default: 'on-track'
    }
  }],
  certifications: [{
    name: String,
    issuer: String,
    validUntil: Date,
    certificate: String // URL to certificate
  }],
  reports: [{
    year: Number,
    title: {
      ar: String,
      en: String
    },
    url: String,
    highlights: [{
      ar: String,
      en: String
    }]
  }]
}, {
  timestamps: true
});

// Index for efficient searching
companyInfoSchema.index({ section: 1 });
companyInfoSchema.index({ isActive: 1, order: 1 });

const CompanyInfo = mongoose.model('CompanyInfo', companyInfoSchema);
const Fleet = mongoose.model('Fleet', fleetSchema);
const Sustainability = mongoose.model('Sustainability', sustainabilitySchema);

module.exports = {
  CompanyInfo,
  Fleet,
  Sustainability
};
