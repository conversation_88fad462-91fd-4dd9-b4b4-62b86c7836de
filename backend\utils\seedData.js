const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import models
const User = require('../models/User');
const Flight = require('../models/Flight');
const Destination = require('../models/Destination');
const Offer = require('../models/Offer');
const Hotel = require('../models/Hotel');
const LoyaltyAccount = require('../models/LoyaltyAccount');

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB Connected for seeding');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
};

// Sample destinations data
const destinationsData = [
  {
    city: 'دبي',
    country: 'الإمارات العربية المتحدة',
    continent: 'Asia',
    airport: {
      code: 'DXB',
      name: 'مطار دبي الدولي',
      terminals: [
        { name: 'Terminal 1', facilities: ['Duty Free', 'Restaurants', 'Lounges'] },
        { name: 'Terminal 2', facilities: ['Duty Free', 'Restaurants'] },
        { name: 'Terminal 3', facilities: ['Duty Free', 'Restaurants', 'Lounges', 'Spa'] }
      ]
    },
    coordinates: { latitude: 25.2532, longitude: 55.3657 },
    timezone: 'Asia/Dubai',
    currency: { code: 'AED', name: 'درهم إماراتي', symbol: 'د.إ' },
    language: { primary: 'Arabic', others: ['English'] },
    weather: {
      averageTemp: {
        summer: { high: 42, low: 29 },
        winter: { high: 26, low: 15 },
        spring: { high: 33, low: 22 },
        autumn: { high: 35, low: 24 }
      },
      rainySeasons: ['December', 'January', 'February'],
      bestTimeToVisit: ['November', 'December', 'January', 'February', 'March']
    },
    attractions: [
      { name: 'برج خليفة', type: 'cultural', description: 'أطول مبنى في العالم', rating: 4.8 },
      { name: 'دبي مول', type: 'shopping', description: 'أكبر مول تجاري في العالم', rating: 4.6 },
      { name: 'نافورة دبي', type: 'entertainment', description: 'عرض مائي موسيقي رائع', rating: 4.7 },
      { name: 'جزر النخلة', type: 'natural', description: 'جزر اصطناعية فريدة', rating: 4.5 }
    ],
    transportation: {
      fromAirport: [
        { type: 'taxi', duration: '15-30 دقيقة', cost: { min: 35, max: 60, currency: 'AED' } },
        { type: 'metro', duration: '45 دقيقة', cost: { min: 8, max: 15, currency: 'AED' } },
        { type: 'bus', duration: '60 دقيقة', cost: { min: 4, max: 8, currency: 'AED' } }
      ],
      local: [
        { type: 'metro', description: 'شبكة مترو حديثة ومكيفة' },
        { type: 'taxi', description: 'تاكسي متوفر على مدار الساعة' },
        { type: 'bus', description: 'شبكة حافلات شاملة' }
      ]
    },
    accommodation: {
      averagePrices: {
        budget: { min: 150, max: 300 },
        midRange: { min: 400, max: 800 },
        luxury: { min: 1000, max: 5000 }
      },
      popularAreas: ['وسط المدينة', 'دبي مارينا', 'جميرا', 'ديرة']
    },
    visaRequirements: {
      visaFree: ['GCC Countries', 'EU', 'USA', 'UK', 'Australia'],
      visaOnArrival: ['India', 'China', 'Russia'],
      eVisa: ['Pakistan', 'Bangladesh'],
      embassy: ['Afghanistan', 'Iraq']
    },
    healthRequirements: {
      vaccinations: [],
      healthInsurance: false,
      covidRequirements: 'اتباع الإرشادات الصحية المحلية'
    },
    images: [
      { url: '/images/destinations/dubai-skyline.jpg', caption: 'أفق دبي الحديث', type: 'city' },
      { url: '/images/destinations/dubai-airport.jpg', caption: 'مطار دبي الدولي', type: 'airport' }
    ],
    isPopular: true,
    searchCount: 1500,
    bookingCount: 800
  },
  {
    city: 'الرياض',
    country: 'المملكة العربية السعودية',
    continent: 'Asia',
    airport: {
      code: 'RUH',
      name: 'مطار الملك خالد الدولي',
      terminals: [
        { name: 'Terminal 1', facilities: ['Duty Free', 'Restaurants'] },
        { name: 'Terminal 2', facilities: ['Duty Free', 'Restaurants', 'Lounges'] }
      ]
    },
    coordinates: { latitude: 24.9576, longitude: 46.6988 },
    timezone: 'Asia/Riyadh',
    currency: { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س' },
    language: { primary: 'Arabic', others: ['English'] },
    isPopular: true,
    searchCount: 1200,
    bookingCount: 650
  },
  {
    city: 'القاهرة',
    country: 'مصر',
    continent: 'Africa',
    airport: {
      code: 'CAI',
      name: 'مطار القاهرة الدولي',
      terminals: [
        { name: 'Terminal 1', facilities: ['Duty Free', 'Restaurants'] },
        { name: 'Terminal 2', facilities: ['Duty Free', 'Restaurants', 'Lounges'] },
        { name: 'Terminal 3', facilities: ['Duty Free', 'Restaurants', 'Lounges'] }
      ]
    },
    coordinates: { latitude: 30.1219, longitude: 31.4056 },
    timezone: 'Africa/Cairo',
    currency: { code: 'EGP', name: 'جنيه مصري', symbol: 'ج.م' },
    language: { primary: 'Arabic', others: ['English'] },
    isPopular: true,
    searchCount: 900,
    bookingCount: 450
  },
  {
    city: 'لندن',
    country: 'المملكة المتحدة',
    continent: 'Europe',
    airport: {
      code: 'LHR',
      name: 'مطار هيثرو',
      terminals: [
        { name: 'Terminal 2', facilities: ['Duty Free', 'Restaurants', 'Lounges'] },
        { name: 'Terminal 3', facilities: ['Duty Free', 'Restaurants', 'Lounges'] },
        { name: 'Terminal 4', facilities: ['Duty Free', 'Restaurants', 'Lounges'] },
        { name: 'Terminal 5', facilities: ['Duty Free', 'Restaurants', 'Lounges', 'Spa'] }
      ]
    },
    coordinates: { latitude: 51.4700, longitude: -0.4543 },
    timezone: 'Europe/London',
    currency: { code: 'GBP', name: 'جنيه إسترليني', symbol: '£' },
    language: { primary: 'English', others: [] },
    isPopular: true,
    searchCount: 2000,
    bookingCount: 1100
  },
  {
    city: 'إسطنبول',
    country: 'تركيا',
    continent: 'Europe',
    airport: {
      code: 'IST',
      name: 'مطار إسطنبول',
      terminals: [
        { name: 'Terminal 1', facilities: ['Duty Free', 'Restaurants', 'Lounges', 'Spa', 'Shopping'] }
      ]
    },
    coordinates: { latitude: 41.2619, longitude: 28.7419 },
    timezone: 'Europe/Istanbul',
    currency: { code: 'TRY', name: 'ليرة تركية', symbol: '₺' },
    language: { primary: 'Turkish', others: ['English', 'Arabic'] },
    weather: {
      averageTemp: {
        summer: { high: 28, low: 20 },
        winter: { high: 9, low: 3 },
        spring: { high: 18, low: 10 },
        autumn: { high: 20, low: 12 }
      },
      rainySeasons: ['November', 'December', 'January'],
      bestTimeToVisit: ['April', 'May', 'September', 'October']
    },
    attractions: [
      { name: 'آيا صوفيا', type: 'historical', description: 'تحفة معمارية بيزنطية وعثمانية', rating: 4.9 },
      { name: 'المسجد الأزرق', type: 'religious', description: 'مسجد السلطان أحمد الشهير', rating: 4.8 },
      { name: 'قصر توب كابي', type: 'historical', description: 'قصر السلاطين العثمانيين', rating: 4.7 },
      { name: 'البازار الكبير', type: 'shopping', description: 'أحد أقدم الأسواق المغطاة في العالم', rating: 4.5 }
    ],
    transportation: {
      fromAirport: [
        { type: 'taxi', duration: '45-60 دقيقة', cost: { min: 150, max: 250, currency: 'TRY' } },
        { type: 'metro', duration: '75 دقيقة', cost: { min: 15, max: 25, currency: 'TRY' } },
        { type: 'bus', duration: '90 دقيقة', cost: { min: 10, max: 20, currency: 'TRY' } }
      ],
      local: [
        { type: 'metro', description: 'شبكة مترو حديثة تربط أجزاء المدينة' },
        { type: 'taxi', description: 'تاكسي أصفر تقليدي وأوبر' },
        { type: 'bus', description: 'شبكة حافلات واسعة' },
        { type: 'bus', description: 'عبارات البوسفور الجميلة' }
      ]
    },
    accommodation: {
      averagePrices: {
        budget: { min: 200, max: 400 },
        midRange: { min: 500, max: 1000 },
        luxury: { min: 1200, max: 3000 }
      },
      popularAreas: ['السلطان أحمد', 'بيوغلو', 'غلطة', 'بشيكتاش']
    },
    visaRequirements: {
      visaFree: ['EU', 'Ukraine', 'Serbia'],
      visaOnArrival: [],
      eVisa: ['USA', 'UK', 'Australia', 'Canada', 'Saudi Arabia', 'UAE'],
      embassy: ['Syria', 'Iraq', 'Afghanistan']
    },
    healthRequirements: {
      vaccinations: [],
      healthInsurance: false,
      covidRequirements: 'اتباع الإرشادات الصحية المحلية'
    },
    images: [
      { url: '/images/destinations/istanbul-bosphorus.jpg', caption: 'مضيق البوسفور', type: 'city' },
      { url: '/images/destinations/istanbul-airport.jpg', caption: 'مطار إسطنبول الجديد', type: 'airport' }
    ],
    isPopular: true,
    searchCount: 1300,
    bookingCount: 750
  },
  {
    city: 'باريس',
    country: 'فرنسا',
    continent: 'Europe',
    airport: {
      code: 'CDG',
      name: 'مطار شارل ديغول',
      terminals: [
        { name: 'Terminal 1', facilities: ['Duty Free', 'Restaurants', 'Lounges'] },
        { name: 'Terminal 2A', facilities: ['Duty Free', 'Restaurants', 'Lounges', 'Spa'] },
        { name: 'Terminal 2B', facilities: ['Duty Free', 'Restaurants'] },
        { name: 'Terminal 2C', facilities: ['Duty Free', 'Restaurants', 'Lounges'] }
      ]
    },
    coordinates: { latitude: 49.0097, longitude: 2.5479 },
    timezone: 'Europe/Paris',
    currency: { code: 'EUR', name: 'يورو', symbol: '€' },
    language: { primary: 'French', others: ['English'] },
    weather: {
      averageTemp: {
        summer: { high: 25, low: 15 },
        winter: { high: 7, low: 2 },
        spring: { high: 16, low: 8 },
        autumn: { high: 17, low: 9 }
      },
      rainySeasons: ['October', 'November', 'December'],
      bestTimeToVisit: ['April', 'May', 'June', 'September', 'October']
    },
    attractions: [
      { name: 'برج إيفل', type: 'architectural', description: 'رمز باريس الشهير', rating: 4.6 },
      { name: 'متحف اللوفر', type: 'cultural', description: 'أكبر متحف فني في العالم', rating: 4.8 },
      { name: 'كاتدرائية نوتردام', type: 'historical', description: 'تحفة معمارية قوطية', rating: 4.7 },
      { name: 'الشانزليزيه', type: 'shopping', description: 'أشهر شارع في باريس', rating: 4.4 }
    ],
    transportation: {
      fromAirport: [
        { type: 'taxi', duration: '45-60 دقيقة', cost: { min: 50, max: 70, currency: 'EUR' } },
        { type: 'train', duration: '35 دقيقة', cost: { min: 10, max: 15, currency: 'EUR' } },
        { type: 'bus', duration: '60 دقيقة', cost: { min: 6, max: 12, currency: 'EUR' } }
      ],
      local: [
        { type: 'metro', description: 'شبكة مترو واسعة ومريحة' },
        { type: 'taxi', description: 'تاكسي باريسي تقليدي' },
        { type: 'bus', description: 'حافلات المدينة' },
        { type: 'bike', description: 'دراجات Vélib المشتركة' }
      ]
    },
    accommodation: {
      averagePrices: {
        budget: { min: 80, max: 150 },
        midRange: { min: 200, max: 400 },
        luxury: { min: 500, max: 2000 }
      },
      popularAreas: ['الدائرة الأولى', 'مارايس', 'سان جيرمان', 'مونمارتر']
    },
    visaRequirements: {
      visaFree: ['EU', 'USA', 'Canada', 'Australia', 'Japan'],
      visaOnArrival: [],
      eVisa: [],
      embassy: ['Most other countries']
    },
    healthRequirements: {
      vaccinations: [],
      healthInsurance: true,
      covidRequirements: 'اتباع الإرشادات الصحية الأوروبية'
    },
    images: [
      { url: '/images/destinations/paris-eiffel.jpg', caption: 'برج إيفل', type: 'city' },
      { url: '/images/destinations/paris-cdg.jpg', caption: 'مطار شارل ديغول', type: 'airport' }
    ],
    isPopular: true,
    searchCount: 1800,
    bookingCount: 950
  },
  {
    city: 'نيويورك',
    country: 'الولايات المتحدة الأمريكية',
    continent: 'North America',
    airport: {
      code: 'JFK',
      name: 'مطار جون كينيدي الدولي',
      terminals: [
        { name: 'Terminal 1', facilities: ['Duty Free', 'Restaurants'] },
        { name: 'Terminal 2', facilities: ['Duty Free', 'Restaurants', 'Lounges'] },
        { name: 'Terminal 4', facilities: ['Duty Free', 'Restaurants', 'Lounges'] },
        { name: 'Terminal 5', facilities: ['Duty Free', 'Restaurants', 'Lounges'] },
        { name: 'Terminal 7', facilities: ['Duty Free', 'Restaurants', 'Lounges'] },
        { name: 'Terminal 8', facilities: ['Duty Free', 'Restaurants', 'Lounges'] }
      ]
    },
    coordinates: { latitude: 40.6413, longitude: -73.7781 },
    timezone: 'America/New_York',
    currency: { code: 'USD', name: 'دولار أمريكي', symbol: '$' },
    language: { primary: 'English', others: ['Spanish'] },
    isPopular: true,
    searchCount: 1800,
    bookingCount: 950
  }
];

// Sample flights data
const flightsData = [
  {
    flightNumber: 'FW101',
    airline: {
      name: 'FlyWay Airlines',
      code: 'FW',
      logo: '/images/flyway-logo.png'
    },
    aircraft: {
      type: 'Boeing 777-300ER',
      capacity: {
        economy: 350,
        business: 42,
        first: 8
      }
    },
    route: {
      departure: {
        airport: {
          code: 'RUH',
          name: 'مطار الملك خالد الدولي',
          city: 'الرياض',
          country: 'المملكة العربية السعودية',
          timezone: 'Asia/Riyadh'
        },
        terminal: 'Terminal 2',
        scheduledTime: new Date('2024-12-25T08:00:00Z')
      },
      arrival: {
        airport: {
          code: 'DXB',
          name: 'مطار دبي الدولي',
          city: 'دبي',
          country: 'الإمارات العربية المتحدة',
          timezone: 'Asia/Dubai'
        },
        terminal: 'Terminal 3',
        scheduledTime: new Date('2024-12-25T10:30:00Z')
      }
    },
    duration: { hours: 1, minutes: 30 },
    pricing: {
      economy: { basePrice: 450, taxes: 50, totalPrice: 500 },
      business: { basePrice: 1200, taxes: 100, totalPrice: 1300 },
      first: { basePrice: 2500, taxes: 150, totalPrice: 2650 }
    },
    availability: {
      economy: 280,
      business: 35,
      first: 6
    },
    services: {
      meals: true,
      wifi: true,
      entertainment: true,
      powerOutlets: true
    }
  },
  {
    flightNumber: 'FW201',
    airline: {
      name: 'FlyWay Airlines',
      code: 'FW',
      logo: '/images/flyway-logo.png'
    },
    aircraft: {
      type: 'Airbus A350-900',
      capacity: {
        economy: 300,
        business: 36,
        first: 6
      }
    },
    route: {
      departure: {
        airport: {
          code: 'DXB',
          name: 'مطار دبي الدولي',
          city: 'دبي',
          country: 'الإمارات العربية المتحدة',
          timezone: 'Asia/Dubai'
        },
        terminal: 'Terminal 3',
        scheduledTime: new Date('2024-12-25T14:00:00Z')
      },
      arrival: {
        airport: {
          code: 'LHR',
          name: 'مطار هيثرو',
          city: 'لندن',
          country: 'المملكة المتحدة',
          timezone: 'Europe/London'
        },
        terminal: 'Terminal 5',
        scheduledTime: new Date('2024-12-25T18:30:00Z')
      }
    },
    duration: { hours: 7, minutes: 30 },
    pricing: {
      economy: { basePrice: 800, taxes: 120, totalPrice: 920 },
      business: { basePrice: 2200, taxes: 200, totalPrice: 2400 },
      first: { basePrice: 4500, taxes: 300, totalPrice: 4800 }
    },
    availability: {
      economy: 250,
      business: 30,
      first: 4
    },
    services: {
      meals: true,
      wifi: true,
      entertainment: true,
      powerOutlets: true
    }
  },
  {
    flightNumber: 'FW301',
    airline: {
      name: 'FlyWay Airlines',
      code: 'FW',
      logo: '/images/flyway-logo.png'
    },
    aircraft: {
      type: 'Boeing 787-9',
      capacity: {
        economy: 290,
        business: 30,
        first: 8
      }
    },
    route: {
      departure: {
        airport: {
          code: 'RUH',
          name: 'مطار الملك خالد الدولي',
          city: 'الرياض',
          country: 'المملكة العربية السعودية',
          timezone: 'Asia/Riyadh'
        },
        terminal: 'Terminal 2',
        scheduledTime: new Date('2024-12-26T02:00:00Z')
      },
      arrival: {
        airport: {
          code: 'IST',
          name: 'مطار إسطنبول',
          city: 'إسطنبول',
          country: 'تركيا',
          timezone: 'Europe/Istanbul'
        },
        terminal: 'Terminal 1',
        scheduledTime: new Date('2024-12-26T06:30:00Z')
      }
    },
    duration: { hours: 4, minutes: 30 },
    pricing: {
      economy: { basePrice: 650, taxes: 80, totalPrice: 730 },
      business: { basePrice: 1800, taxes: 150, totalPrice: 1950 },
      first: { basePrice: 3500, taxes: 250, totalPrice: 3750 }
    },
    availability: {
      economy: 240,
      business: 25,
      first: 6
    },
    services: {
      meals: true,
      wifi: true,
      entertainment: true,
      powerOutlets: true
    }
  },
  {
    flightNumber: 'FW401',
    airline: {
      name: 'FlyWay Airlines',
      code: 'FW',
      logo: '/images/flyway-logo.png'
    },
    aircraft: {
      type: 'Airbus A330-300',
      capacity: {
        economy: 250,
        business: 28,
        first: 0
      }
    },
    route: {
      departure: {
        airport: {
          code: 'CAI',
          name: 'مطار القاهرة الدولي',
          city: 'القاهرة',
          country: 'مصر',
          timezone: 'Africa/Cairo'
        },
        terminal: 'Terminal 3',
        scheduledTime: new Date('2024-12-26T10:00:00Z')
      },
      arrival: {
        airport: {
          code: 'CDG',
          name: 'مطار شارل ديغول',
          city: 'باريس',
          country: 'فرنسا',
          timezone: 'Europe/Paris'
        },
        terminal: 'Terminal 2A',
        scheduledTime: new Date('2024-12-26T14:30:00Z')
      }
    },
    duration: { hours: 4, minutes: 30 },
    pricing: {
      economy: { basePrice: 750, taxes: 100, totalPrice: 850 },
      business: { basePrice: 2000, taxes: 180, totalPrice: 2180 },
      first: { basePrice: 0, taxes: 0, totalPrice: 0 }
    },
    availability: {
      economy: 200,
      business: 22,
      first: 0
    },
    services: {
      meals: true,
      wifi: false,
      entertainment: true,
      powerOutlets: false
    }
  }
];

// Sample offers data
const offersData = [
  {
    title: {
      ar: 'خصم 25% على جميع الرحلات الدولية',
      en: '25% Off All International Flights'
    },
    description: {
      ar: 'احصل على خصم 25% على جميع الرحلات الدولية. العرض ساري لفترة محدودة!',
      en: 'Get 25% discount on all international flights. Limited time offer!'
    },
    type: 'flight-discount',
    discount: {
      type: 'percentage',
      value: 25,
      maxDiscount: 500
    },
    conditions: {
      minBookingAmount: 300,
      maxUsagePerUser: 2,
      totalUsageLimit: 1000,
      applicableClasses: ['economy', 'business'],
      advanceBookingDays: 7
    },
    validity: {
      startDate: new Date('2024-12-01'),
      endDate: new Date('2025-03-31'),
      travelPeriod: {
        start: new Date('2024-12-15'),
        end: new Date('2025-04-30')
      }
    },
    promoCode: 'WINTER25',
    images: [
      {
        url: '/images/offers/winter-sale.jpg',
        alt: 'عرض الشتاء',
        type: 'banner'
      }
    ],
    priority: 10,
    isActive: true,
    isFeatured: true,
    usageCount: 45,
    clickCount: 1250
  },
  {
    title: {
      ar: 'عرض العائلة - احجز لـ 4 واحصل على خصم 30%',
      en: 'Family Deal - Book for 4 and Get 30% Off'
    },
    description: {
      ar: 'عرض خاص للعائلات! احجز لأربعة أشخاص أو أكثر واحصل على خصم 30%',
      en: 'Special family offer! Book for 4 or more passengers and get 30% off'
    },
    type: 'group-booking',
    discount: {
      type: 'percentage',
      value: 30,
      maxDiscount: 800
    },
    conditions: {
      minBookingAmount: 800,
      maxUsagePerUser: 1,
      totalUsageLimit: 500,
      applicableClasses: ['economy'],
      advanceBookingDays: 14
    },
    validity: {
      startDate: new Date('2024-12-01'),
      endDate: new Date('2025-02-28')
    },
    promoCode: 'FAMILY30',
    images: [
      {
        url: '/images/offers/family-deal.jpg',
        alt: 'عرض العائلة',
        type: 'banner'
      }
    ],
    priority: 8,
    isActive: true,
    isFeatured: true,
    usageCount: 23,
    clickCount: 890
  },
  {
    title: {
      ar: 'خصم 100 دولار على الرحلات لأعضاء الذهب',
      en: '$100 Off for Gold Members'
    },
    description: {
      ar: 'خصم حصري لأعضاء الولاء من مستوى الذهب وما فوق',
      en: 'Exclusive discount for Gold tier loyalty members and above'
    },
    type: 'loyalty-bonus',
    discount: {
      type: 'fixed-amount',
      value: 100
    },
    conditions: {
      minBookingAmount: 500,
      maxUsagePerUser: 3,
      loyaltyTierRequired: 'Gold',
      applicableClasses: ['economy', 'business', 'first']
    },
    validity: {
      startDate: new Date('2024-12-01'),
      endDate: new Date('2025-12-31')
    },
    promoCode: 'GOLD100',
    images: [
      {
        url: '/images/offers/gold-member.jpg',
        alt: 'عرض الأعضاء الذهبيين',
        type: 'banner'
      }
    ],
    priority: 6,
    isActive: true,
    isFeatured: false,
    usageCount: 67,
    clickCount: 456
  }
];

// Sample hotels data (will be populated with destination IDs after destinations are created)
const hotelsData = [
  {
    name: 'برج العرب جميرا',
    description: {
      ar: 'فندق فاخر على شكل شراع في دبي مع إطلالة خلابة على الخليج العربي',
      en: 'Luxury sail-shaped hotel in Dubai with stunning Arabian Gulf views'
    },
    starRating: 5,
    rating: {
      average: 9.2,
      reviewCount: 2847
    },
    amenities: ['wifi', 'pool', 'spa', 'restaurant', 'bar', 'room-service', 'concierge', 'business-center'],
    roomTypes: [
      {
        name: 'جناح ديلوكس',
        description: 'جناح فاخر مع إطلالة على البحر',
        capacity: { adults: 2, children: 2 },
        bedType: 'king',
        size: 170,
        amenities: ['sea-view', 'balcony', 'minibar', 'safe'],
        images: ['/images/hotels/burj-al-arab-suite.jpg'],
        pricing: {
          basePrice: 2500,
          currency: 'USD',
          taxes: 250,
          fees: 50
        },
        availability: 5
      }
    ],
    policies: {
      checkIn: { time: '15:00', minAge: 18 },
      checkOut: { time: '12:00' },
      cancellation: 'partial',
      pets: { allowed: false },
      smoking: 'non-smoking'
    },
    contact: {
      phone: '+971-4-301-7777',
      email: '<EMAIL>',
      website: 'https://www.jumeirah.com/burj-al-arab'
    },
    images: [
      { url: '/images/hotels/burj-al-arab-exterior.jpg', caption: 'برج العرب من الخارج', type: 'exterior' },
      { url: '/images/hotels/burj-al-arab-lobby.jpg', caption: 'اللوبي الفاخر', type: 'lobby' }
    ],
    nearbyAttractions: [
      { name: 'جزيرة النخلة', distance: 5, type: 'attraction' },
      { name: 'دبي مول', distance: 15, type: 'shopping' }
    ],
    transportation: {
      airportDistance: 25,
      airportTransfer: {
        available: true,
        type: 'luxury-car',
        duration: '30 دقيقة',
        cost: 200
      }
    },
    isActive: true,
    isPartner: true,
    partnerDiscount: 15
  },
  {
    name: 'فندق الريتز كارلتون الرياض',
    description: {
      ar: 'فندق فاخر في قلب الرياض مع خدمة استثنائية ومرافق عالمية المستوى',
      en: 'Luxury hotel in the heart of Riyadh with exceptional service and world-class facilities'
    },
    starRating: 5,
    rating: {
      average: 8.9,
      reviewCount: 1654
    },
    amenities: ['wifi', 'pool', 'gym', 'spa', 'restaurant', 'bar', 'room-service', 'concierge', 'business-center'],
    roomTypes: [
      {
        name: 'غرفة ديلوكس',
        description: 'غرفة أنيقة مع إطلالة على المدينة',
        capacity: { adults: 2, children: 1 },
        bedType: 'king',
        size: 45,
        amenities: ['city-view', 'minibar', 'safe', 'work-desk'],
        images: ['/images/hotels/ritz-riyadh-room.jpg'],
        pricing: {
          basePrice: 450,
          currency: 'USD',
          taxes: 45,
          fees: 20
        },
        availability: 12
      },
      {
        name: 'جناح تنفيذي',
        description: 'جناح واسع مع صالة منفصلة',
        capacity: { adults: 3, children: 2 },
        bedType: 'king',
        size: 85,
        amenities: ['city-view', 'living-room', 'minibar', 'safe', 'work-desk'],
        images: ['/images/hotels/ritz-riyadh-suite.jpg'],
        pricing: {
          basePrice: 750,
          currency: 'USD',
          taxes: 75,
          fees: 30
        },
        availability: 6
      }
    ],
    policies: {
      checkIn: { time: '15:00', minAge: 18 },
      checkOut: { time: '12:00' },
      cancellation: 'partial',
      pets: { allowed: false },
      smoking: 'non-smoking'
    },
    contact: {
      phone: '+966-11-802-8888',
      email: '<EMAIL>',
      website: 'https://www.ritzcarlton.com/riyadh'
    },
    images: [
      { url: '/images/hotels/ritz-riyadh-exterior.jpg', caption: 'فندق الريتز كارلتون الرياض', type: 'exterior' },
      { url: '/images/hotels/ritz-riyadh-pool.jpg', caption: 'المسبح', type: 'amenity' }
    ],
    nearbyAttractions: [
      { name: 'برج المملكة', distance: 2, type: 'landmark' },
      { name: 'الدرعية التاريخية', distance: 20, type: 'historical' }
    ],
    transportation: {
      airportDistance: 35,
      airportTransfer: {
        available: true,
        type: 'luxury-car',
        duration: '45 دقيقة',
        cost: 150
      }
    },
    isActive: true,
    isPartner: true,
    partnerDiscount: 12
  }
];

// Seed function
const seedData = async () => {
  try {
    console.log('🌱 Starting data seeding...');

    // Clear existing data
    await User.deleteMany({});
    await Flight.deleteMany({});
    await Destination.deleteMany({});
    await Offer.deleteMany({});
    await Hotel.deleteMany({});
    await LoyaltyAccount.deleteMany({});

    console.log('🗑️ Cleared existing data');

    // Insert destinations
    const destinations = await Destination.insertMany(destinationsData);
    console.log(`✅ Inserted ${destinations.length} destinations`);

    // Insert flights
    const flights = await Flight.insertMany(flightsData);
    console.log(`✅ Inserted ${flights.length} flights`);

    // Insert offers
    const offers = await Offer.insertMany(offersData);
    console.log(`✅ Inserted ${offers.length} offers`);

    // Link hotels to destinations and insert
    const dubaiDestination = destinations.find(d => d.airport.code === 'DXB');
    const riyadhDestination = destinations.find(d => d.airport.code === 'RUH');

    if (dubaiDestination) {
      hotelsData[0].destination = dubaiDestination._id;
    }
    if (riyadhDestination) {
      hotelsData[1].destination = riyadhDestination._id;
    }

    const hotels = await Hotel.insertMany(hotelsData);
    console.log(`✅ Inserted ${hotels.length} hotels`);

    console.log('🎉 Data seeding completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('❌ Error seeding data:', error);
    process.exit(1);
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  connectDB().then(() => {
    seedData();
  });
}

module.exports = { seedData };
