const LoyaltyAccount = require('../models/LoyaltyAccount');
const User = require('../models/User');
const { asyncHandler } = require('../middlewares/errorHandler');

// @desc    Get user loyalty account
// @route   GET /api/loyalty/account
// @access  Private
const getLoyaltyAccount = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id).populate('loyaltyAccount');

  if (!user.loyaltyAccount) {
    return res.status(404).json({
      success: false,
      message: 'حساب الولاء غير موجود'
    });
  }

  // Calculate tier and update benefits
  user.loyaltyAccount.calculateTier();
  await user.loyaltyAccount.save();

  res.status(200).json({
    success: true,
    data: {
      loyaltyAccount: user.loyaltyAccount
    }
  });
});

// @desc    Get loyalty tiers information
// @route   GET /api/loyalty/tiers
// @access  Public
const getLoyaltyTiers = asyncHandler(async (req, res) => {
  const tiers = [
    {
      name: 'Bronze',
      requirement: 0,
      benefits: {
        priorityCheckin: false,
        loungeAccess: false,
        extraBaggage: 0,
        seatUpgrade: false,
        priorityBoarding: false,
        bonusPointsMultiplier: 1
      },
      color: '#CD7F32',
      description: 'المستوى الأساسي لجميع الأعضاء الجدد'
    },
    {
      name: 'Silver',
      requirement: 5000,
      benefits: {
        priorityCheckin: true,
        loungeAccess: false,
        extraBaggage: 1,
        seatUpgrade: false,
        priorityBoarding: false,
        bonusPointsMultiplier: 1.5
      },
      color: '#C0C0C0',
      description: 'مزايا إضافية مع تسجيل دخول أولوية'
    },
    {
      name: 'Gold',
      requirement: 15000,
      benefits: {
        priorityCheckin: true,
        loungeAccess: false,
        extraBaggage: 1,
        seatUpgrade: false,
        priorityBoarding: true,
        bonusPointsMultiplier: 2
      },
      color: '#FFD700',
      description: 'صعود أولوية ونقاط مضاعفة'
    },
    {
      name: 'Platinum',
      requirement: 25000,
      benefits: {
        priorityCheckin: true,
        loungeAccess: true,
        extraBaggage: 2,
        seatUpgrade: true,
        priorityBoarding: true,
        bonusPointsMultiplier: 2.5
      },
      color: '#E5E4E2',
      description: 'دخول صالات المطار وترقية المقاعد'
    },
    {
      name: 'Diamond',
      requirement: 50000,
      benefits: {
        priorityCheckin: true,
        loungeAccess: true,
        extraBaggage: 3,
        seatUpgrade: true,
        priorityBoarding: true,
        bonusPointsMultiplier: 3
      },
      color: '#B9F2FF',
      description: 'أعلى مستوى مع جميع المزايا الحصرية'
    }
  ];

  res.status(200).json({
    success: true,
    data: {
      tiers
    }
  });
});

// @desc    Redeem points
// @route   POST /api/loyalty/redeem
// @access  Private
const redeemPoints = asyncHandler(async (req, res) => {
  const { points, rewardType, description } = req.body;

  const user = await User.findById(req.user.id).populate('loyaltyAccount');

  if (!user.loyaltyAccount) {
    return res.status(404).json({
      success: false,
      message: 'حساب الولاء غير موجود'
    });
  }

  const loyaltyAccount = user.loyaltyAccount;

  // Check if user has enough points
  if (loyaltyAccount.points.current < points) {
    return res.status(400).json({
      success: false,
      message: 'نقاط غير كافية للاستبدال'
    });
  }

  // Minimum redemption amount
  if (points < 1000) {
    return res.status(400).json({
      success: false,
      message: 'الحد الأدنى للاستبدال 1000 نقطة'
    });
  }

  // Deduct points
  loyaltyAccount.points.current -= points;

  // Add transaction
  loyaltyAccount.transactions.push({
    type: 'redeemed',
    points: -points,
    description: description || `استبدال ${points} نقطة - ${rewardType}`,
    createdAt: new Date()
  });

  await loyaltyAccount.save();

  res.status(200).json({
    success: true,
    message: 'تم استبدال النقاط بنجاح',
    data: {
      pointsRedeemed: points,
      remainingPoints: loyaltyAccount.points.current,
      rewardType
    }
  });
});

// @desc    Get loyalty transactions
// @route   GET /api/loyalty/transactions
// @access  Private
const getLoyaltyTransactions = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;

  const user = await User.findById(req.user.id).populate('loyaltyAccount');

  if (!user.loyaltyAccount) {
    return res.status(404).json({
      success: false,
      message: 'حساب الولاء غير موجود'
    });
  }

  const loyaltyAccount = user.loyaltyAccount;
  const transactions = loyaltyAccount.transactions
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice((page - 1) * limit, page * limit);

  const total = loyaltyAccount.transactions.length;

  res.status(200).json({
    success: true,
    data: {
      transactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// @desc    Get available rewards
// @route   GET /api/loyalty/rewards
// @access  Private
const getAvailableRewards = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id).populate('loyaltyAccount');

  if (!user.loyaltyAccount) {
    return res.status(404).json({
      success: false,
      message: 'حساب الولاء غير موجود'
    });
  }

  const currentPoints = user.loyaltyAccount.points.current;

  const rewards = [
    {
      id: 'flight-discount-10',
      name: 'خصم 10% على الرحلة التالية',
      pointsRequired: 2000,
      type: 'flight-discount',
      value: 10,
      available: currentPoints >= 2000,
      description: 'خصم 10% على أي رحلة قادمة'
    },
    {
      id: 'flight-discount-20',
      name: 'خصم 20% على الرحلة التالية',
      pointsRequired: 4000,
      type: 'flight-discount',
      value: 20,
      available: currentPoints >= 4000,
      description: 'خصم 20% على أي رحلة قادمة'
    },
    {
      id: 'free-baggage',
      name: 'حقيبة إضافية مجانية',
      pointsRequired: 1500,
      type: 'baggage',
      value: 1,
      available: currentPoints >= 1500,
      description: 'حقيبة مسجلة إضافية مجانية'
    },
    {
      id: 'lounge-access',
      name: 'دخول صالة المطار',
      pointsRequired: 3000,
      type: 'lounge',
      value: 1,
      available: currentPoints >= 3000,
      description: 'دخول مجاني لصالة المطار لمرة واحدة'
    },
    {
      id: 'seat-upgrade',
      name: 'ترقية المقعد',
      pointsRequired: 5000,
      type: 'seat-upgrade',
      value: 1,
      available: currentPoints >= 5000,
      description: 'ترقية مجانية لدرجة أعلى'
    },
    {
      id: 'free-meal',
      name: 'وجبة مجانية',
      pointsRequired: 1000,
      type: 'meal',
      value: 1,
      available: currentPoints >= 1000,
      description: 'وجبة مجانية على متن الطائرة'
    }
  ];

  res.status(200).json({
    success: true,
    data: {
      currentPoints,
      rewards: rewards.filter(reward => reward.available),
      allRewards: rewards
    }
  });
});

// @desc    Get loyalty partners
// @route   GET /api/loyalty/partners
// @access  Public
const getLoyaltyPartners = asyncHandler(async (req, res) => {
  const partners = [
    {
      name: 'فنادق هيلتون',
      category: 'hotels',
      pointsEarned: '2 نقطة لكل دولار',
      description: 'اكسب نقاط إضافية عند الإقامة في فنادق هيلتون',
      logo: '/images/partners/hilton.png'
    },
    {
      name: 'هيرتز لتأجير السيارات',
      category: 'car-rental',
      pointsEarned: '3 نقاط لكل دولار',
      description: 'نقاط مضاعفة عند استئجار السيارات',
      logo: '/images/partners/hertz.png'
    },
    {
      name: 'ماستركارد',
      category: 'credit-card',
      pointsEarned: '1 نقطة لكل دولار',
      description: 'اكسب نقاط على جميع مشترياتك',
      logo: '/images/partners/mastercard.png'
    },
    {
      name: 'مطاعم الشرق الأوسط',
      category: 'dining',
      pointsEarned: '2 نقطة لكل دولار',
      description: 'نقاط إضافية في المطاعم المشاركة',
      logo: '/images/partners/restaurants.png'
    }
  ];

  res.status(200).json({
    success: true,
    data: {
      partners
    }
  });
});

module.exports = {
  getLoyaltyAccount,
  getLoyaltyTiers,
  redeemPoints,
  getLoyaltyTransactions,
  getAvailableRewards,
  getLoyaltyPartners
};
