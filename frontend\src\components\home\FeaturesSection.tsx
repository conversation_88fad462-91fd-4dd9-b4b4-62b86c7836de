import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { 
  Clock, 
  Shield, 
  Award, 
  Headphones, 
  Globe, 
  CreditCard,
  Smartphone,
  Users
} from 'lucide-react';

const FeaturesSection: React.FC = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const features = [
    {
      icon: Clock,
      title: isRTL ? 'حجز سريع' : 'Quick Booking',
      description: isRTL ? 'احجز رحلتك في أقل من 3 دقائق' : 'Book your flight in less than 3 minutes',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      icon: Shield,
      title: isRTL ? 'دفع آمن' : 'Secure Payment',
      description: isRTL ? 'حماية كاملة لبياناتك المالية' : 'Complete protection for your financial data',
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      icon: Award,
      title: isRTL ? 'أفضل الأسعار' : 'Best Prices',
      description: isRTL ? 'ضمان أفضل الأسعار في السوق' : 'Guaranteed best prices in the market',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      icon: Headphones,
      title: isRTL ? 'دعم 24/7' : '24/7 Support',
      description: isRTL ? 'فريق دعم متاح على مدار الساعة' : 'Support team available around the clock',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      icon: Globe,
      title: isRTL ? '85+ وجهة' : '85+ Destinations',
      description: isRTL ? 'وجهات حول العالم في انتظارك' : 'Destinations around the world await you',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100'
    },
    {
      icon: CreditCard,
      title: isRTL ? 'طرق دفع متعددة' : 'Multiple Payment Methods',
      description: isRTL ? 'ادفع بالطريقة التي تناسبك' : 'Pay the way that suits you',
      color: 'text-pink-600',
      bgColor: 'bg-pink-100'
    },
    {
      icon: Smartphone,
      title: isRTL ? 'تطبيق محمول' : 'Mobile App',
      description: isRTL ? 'احجز من أي مكان وفي أي وقت' : 'Book from anywhere, anytime',
      color: 'text-teal-600',
      bgColor: 'bg-teal-100'
    },
    {
      icon: Users,
      title: isRTL ? '15M+ عميل' : '15M+ Customers',
      description: isRTL ? 'ملايين العملاء يثقون بنا' : 'Millions of customers trust us',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="heading-2 mb-6">
            {isRTL ? 'لماذا تختار طرق السماء؟' : 'Why Choose FlyWay?'}
          </h2>
          <p className="text-body-lg max-w-3xl mx-auto">
            {isRTL 
              ? 'نحن نقدم تجربة سفر استثنائية مع أفضل الخدمات والمميزات التي تجعل رحلتك مريحة وممتعة'
              : 'We provide an exceptional travel experience with the best services and features that make your journey comfortable and enjoyable'
            }
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group text-center"
            >
              <div className="relative mb-6">
                {/* Icon Background */}
                <div className={`w-20 h-20 ${feature.bgColor} rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className={`w-10 h-10 ${feature.color}`} />
                </div>
                
                {/* Floating Animation */}
                <motion.div
                  animate={{ y: [0, -5, 0] }}
                  transition={{ duration: 2, repeat: Infinity, delay: index * 0.2 }}
                  className="absolute inset-0"
                />
              </div>

              <h3 className="text-xl font-semibold text-secondary-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">
                {feature.title}
              </h3>
              
              <p className="text-secondary-600 leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-20 bg-gradient-to-r from-primary-50 to-accent-50 rounded-3xl p-8 lg:p-12"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            {[
              { 
                number: '99.8%', 
                label: isRTL ? 'معدل الرضا' : 'Satisfaction Rate',
                description: isRTL ? 'من عملائنا راضون عن خدماتنا' : 'of our customers are satisfied with our services'
              },
              { 
                number: '2.5M', 
                label: isRTL ? 'رحلة سنوياً' : 'Flights Annually',
                description: isRTL ? 'رحلة نقوم بتشغيلها كل عام' : 'flights we operate every year'
              },
              { 
                number: '45s', 
                label: isRTL ? 'متوسط الحجز' : 'Average Booking',
                description: isRTL ? 'متوسط وقت إتمام الحجز' : 'average time to complete booking'
              },
              { 
                number: '24/7', 
                label: isRTL ? 'خدمة العملاء' : 'Customer Service',
                description: isRTL ? 'دعم متواصل على مدار الساعة' : 'continuous support around the clock'
              }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="text-4xl lg:text-5xl font-bold text-primary-600 mb-2 group-hover:scale-110 transition-transform duration-300">
                  {stat.number}
                </div>
                <div className="text-lg font-semibold text-secondary-900 mb-1">
                  {stat.label}
                </div>
                <div className="text-sm text-secondary-600">
                  {stat.description}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturesSection;
