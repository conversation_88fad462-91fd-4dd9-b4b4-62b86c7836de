import React from 'react'

const PopularDestinations: React.FC = () => {
  const destinations = [
    {
      id: 1,
      name: 'دبي',
      country: 'الإمارات العربية المتحدة',
      price: 'من 450$',
      image: 'https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=400&h=300&fit=crop',
      description: 'مدينة المستقبل والتسوق الفاخر'
    },
    {
      id: 2,
      name: 'إسطنبول',
      country: 'تركيا',
      price: 'من 280$',
      image: 'https://images.unsplash.com/photo-1541432901042-2d8bd64b4a9b?w=400&h=300&fit=crop',
      description: 'حيث يلتقي التاريخ بالحداثة'
    },
    {
      id: 3,
      name: 'باريس',
      country: 'فرنسا',
      price: 'من 520$',
      image: 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=400&h=300&fit=crop',
      description: 'مدينة الأنوار والرومانسية'
    },
    {
      id: 4,
      name: 'لندن',
      country: 'المملكة المتحدة',
      price: 'من 480$',
      image: 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=400&h=300&fit=crop',
      description: 'التاريخ العريق والثقافة الغنية'
    },
    {
      id: 5,
      name: 'القاهرة',
      country: 'مصر',
      price: 'من 180$',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=300&fit=crop',
      description: 'أم الدنيا وحضارة الفراعنة'
    },
    {
      id: 6,
      name: 'كوالالمبور',
      country: 'ماليزيا',
      price: 'من 380$',
      image: 'https://images.unsplash.com/photo-1596422846543-75c6fc197f07?w=400&h=300&fit=crop',
      description: 'التنوع الثقافي والطبيعة الخلابة'
    }
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">الوجهات الشائعة</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            اكتشف أجمل الوجهات حول العالم مع عروض حصرية وأسعار لا تُقاوم
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {destinations.map((destination) => (
            <div key={destination.id} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="relative">
                <img
                  src={destination.image}
                  alt={destination.name}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 right-4 bg-skyway-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  {destination.price}
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-2">{destination.name}</h3>
                <p className="text-gray-600 mb-2">{destination.country}</p>
                <p className="text-gray-500 text-sm mb-4">{destination.description}</p>
                
                <button className="w-full bg-gradient-to-r from-skyway-600 to-skyway-700 text-white py-3 rounded-lg font-semibold hover:from-skyway-700 hover:to-skyway-800 transition-all duration-300">
                  احجز الآن
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button className="bg-white text-skyway-600 border-2 border-skyway-600 px-8 py-3 rounded-lg font-semibold hover:bg-skyway-600 hover:text-white transition-all duration-300">
            عرض جميع الوجهات
          </button>
        </div>
      </div>
    </section>
  )
}

export default PopularDestinations
