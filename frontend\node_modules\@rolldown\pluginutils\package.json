{"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9", "license": "MIT", "type": "module", "repository": {"type": "git", "url": "git+https://github.com/rolldown/rolldown.git", "directory": "packages/pluginutils"}, "publishConfig": {"access": "public"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist"], "devDependencies": {"@types/picomatch": "^4.0.0", "picomatch": "^4.0.2", "tsdown": "0.11.11", "vitest": "^3.0.1"}, "scripts": {"build": "tsdown", "test": "vitest --typecheck"}}